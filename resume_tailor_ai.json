{"nodes": [{"parameters": {"httpMethod": "POST", "path": "webhook", "options": {}}, "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "uuid": "webhookTrigger"}, {"parameters": {"file": "={{ $json.file.data }}", "options": {}}, "name": "PDF Parser", "type": "n8n-nodes-base.pdfParser", "typeVersion": 1, "uuid": "pdf<PERSON><PERSON><PERSON>"}, {"parameters": {"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "Analyze the following job description and resume text. Provide suggestions on how to tailor the resume to better fit the job description. Highlight key areas for improvement. Job Description: {{ $json.jobDescription }}\n\nResume Text: {{ $json.text }}"}], "options": {}}, "name": "GPT-3.5 Resume Tailor", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "uuid": "gpt35ResumeTailor", "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "Resume Tailoring Suggestions", "text": "Here are your resume tailoring suggestions:\n\n{{ $json.choices[0].message.content }}", "options": {}}, "name": "Send Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "uuid": "sendEmail", "credentials": {"smtpAuth": {"id": "YOUR_SMTP_CREDENTIAL_ID", "name": "SMTP Account"}}}], "connections": {"webhookTrigger": {"main": [[{"node": "PDF Parser", "type": "main", "index": 0}]]}, "pdfParser": {"main": [[{"node": "GPT-3.5 Resume Tailor", "type": "main", "index": 0}]]}, "gpt35ResumeTailor": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}}, "name": "Resume-Tailor AI", "active": false, "nodesData": {}, "settings": {"errorWorkflow": "YOUR_ERROR_WORKFLOW_ID"}, "version": 1}