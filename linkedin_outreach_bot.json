{"nodes": [{"parameters": {"mode": "everyHour", "value": 1, "options": {}}, "name": "Schedule Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "uuid": "scheduleTrigger"}, {"parameters": {"authentication": "accessToken", "spreadsheetId": "YOUR_GOOGLE_SHEET_ID", "sheetName": "LinkedIn Profiles", "operation": "getAll", "options": {}}, "name": "Google Sheets Read Profiles", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "uuid": "googleSheetsReadProfiles", "credentials": {"googleSheetsOAuth2Api": {"id": "YOUR_GOOGLE_SHEETS_CREDENTIAL_ID", "name": "Google Sheets Account"}}}, {"parameters": {"model": "gpt-4", "messages": [{"role": "user", "content": "Generate a personalized LinkedIn outreach message for the following profile. Keep it concise and professional, focusing on potential collaboration or shared interests. Profile: {{ $json.profileData }}"}], "options": {}}, "name": "GPT-4 Message Generator", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "uuid": "gpt4MessageGenerator", "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"url": "https://api.linkedin.com/v2/messages", "method": "POST", "jsonBody": true, "body": {"recipients": [{"person": "urn:li:person:{{ $json.linkedinId }}"}], "body": {"text": "{{ $json.choices[0].message.content }}"}}, "options": {"sendHeaders": true, "headerParameters": [{"name": "Authorization", "value": "Bearer YOUR_LINKEDIN_ACCESS_TOKEN"}, {"name": "Content-Type", "value": "application/json"}]}}, "name": "LinkedIn API Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "uuid": "linkedinApiRequest"}], "connections": {"scheduleTrigger": {"main": [[{"node": "Google Sheets Read Profiles", "type": "main", "index": 0}]]}, "googleSheetsReadProfiles": {"main": [[{"node": "GPT-4 Message Generator", "type": "main", "index": 0}]]}, "gpt4MessageGenerator": {"main": [[{"node": "LinkedIn API Request", "type": "main", "index": 0}]]}}, "name": "LinkedIn-Outreach Bot", "active": false, "nodesData": {}, "settings": {"errorWorkflow": "YOUR_ERROR_WORKFLOW_ID"}, "version": 1}