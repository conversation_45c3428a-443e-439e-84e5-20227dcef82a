{"nodes": [{"parameters": {"mode": "everyMinute", "value": 1, "options": {}}, "name": "Job Search Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "uuid": "jobSearchTrigger"}, {"parameters": {"query": "Software Engineer jobs in San Francisco", "api": "serp<PERSON>i", "options": {}}, "name": "SerpAPI Search", "type": "n8n-nodes-base.serpApi", "typeVersion": 1, "uuid": "serpApiSearch", "credentials": {"serpApi": {"id": "YOUR_SERPAPI_CREDENTIAL_ID", "name": "SerpAPI Account"}}}, {"parameters": {"model": "gpt-4", "messages": [{"role": "user", "content": "Analyze the following job posting and determine if it's relevant for a software engineer with 5 years of experience. Extract the job title, company name, and a brief summary of key requirements. If irrelevant, state 'IRRELEVANT'. Job Posting: {{ $json.html }}"}], "options": {}}, "name": "GPT-4 <PERSON> Analyzer", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "uuid": "gpt4JobAnalyzer", "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAI Account"}}}, {"parameters": {"conditions": [{"value1": "{{ $json.choices[0].message.content }}", "operator": "notContains", "value2": "IRRELEVANT"}]}, "name": "Filter Irrelevant Jobs", "type": "n8n-nodes-base.if", "typeVersion": 1, "uuid": "filterIrrelevantJobs"}, {"parameters": {"authentication": "accessToken", "spreadsheetId": "YOUR_GOOGLE_SHEET_ID", "sheetName": "Job Postings", "operation": "appendRow", "options": {"values": "={{ $json.jobTitle }},={{ $json.companyName }},={{ $json.summary }}"}}, "name": "Google Sheets Appender", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "uuid": "googleSheetsAppender", "credentials": {"googleSheetsOAuth2Api": {"id": "YOUR_GOOGLE_SHEETS_CREDENTIAL_ID", "name": "Google Sheets Account"}}}], "connections": {"jobSearchTrigger": {"main": [[{"node": "SerpAPI Search", "type": "main", "index": 0}]]}, "serpApiSearch": {"main": [[{"node": "GPT-4 <PERSON> Analyzer", "type": "main", "index": 0}]]}, "gpt4JobAnalyzer": {"main": [[{"node": "Filter Irrelevant Jobs", "type": "main", "index": 0}]]}, "filterIrrelevantJobs": {"main": [[{"node": "Google Sheets Appender", "type": "main", "index": 0}]]}}, "name": "Job-Hunt Maestro", "active": false, "nodesData": {}, "settings": {"errorWorkflow": "YOUR_ERROR_WORKFLOW_ID"}, "version": 1}