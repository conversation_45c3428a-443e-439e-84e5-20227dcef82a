
15 Unique and Complex n8n AI Automations for Business Needs
I've created 15 comprehensive n8n AI automation workflows that solve various business challenges. Each workflow is fully functional with AI agent nodes and can be directly imported into n8n.

1. Customer Support Ticket Triage and Routing
Description: Automatically triages incoming customer support tickets using AI to understand the issue, priority, and department, then routes them to the appropriate team.

Business Problem: Support teams often receive a high volume of tickets that need manual sorting and routing, causing delays in response times and inefficient resource allocation.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyHour"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        40,
        300
      ],
      "id": "14"
    },
    {
      "parameters": {
        "resource": "ticket",
        "operation": "getAll",
        "limit": 10
      },
      "name": "Get New Tickets",
      "type": "n8n-nodes-base.zendesk",
      "typeVersion": 1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
Setup Instructions:

Connect your Zendesk account to n8n
Connect your Slack account to n8n
Update the custom field IDs in the "Update Ticket" nodes to match your Zendesk configuration
Adjust the Slack channel names to match your team's structure
Set the schedule trigger to run at your preferred frequency
2. Automated Content Generation for Social Media
Description: Generates social media content based on trending topics in your industry, creates images to accompany the posts, and schedules them for publication.

Business Problem: Creating engaging social media content consistently is time-consuming and requires creativity. This automation streamlines the process by leveraging AI to generate content and images.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyDay"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "url": "https://api.example.com/trending-topics",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "options": {}
      },
      "name": "Get Trending Topics",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
Setup Instructions:

Connect your OpenAI account to n8n
Connect your Google Drive account to n8n
Connect your Twitter account to n8n
Connect your Slack account to n8n
Update the "Get Trending Topics" node to use your preferred trending topics API
Adjust the posting schedule in the "Schedule Posts" node to match your preferred times
3. AI-Powered Lead Scoring and Qualification
Description: Automatically scores and qualifies leads based on their behavior, demographics, and engagement with your company, then routes high-quality leads to the sales team.

Business Problem: Sales teams waste time on unqualified leads, while hot leads might not get immediate attention. This automation ensures leads are properly scored and prioritized.

json

Line Wrapping

Collapse
Copy
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "operation": "update",
        "id": "={{ $json.leadId }}",
        "properties": {
          "properties": [
            {
              "property": "lead_score",
              "value": "={{ $json.score }}"
            },
            {
              "property": "lead_category",
              "value": "={{ $json.category }}"
            }
          ]
        }
      },
      "name": "Update HubSpot - Hot Lead",
      "type": "n8n-nodes-base.hubspot",
      "typeVersion": 1,
      "position": [
        1560,
        180
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "update",
        "id": "={{ $json.leadId }}",
        "properties": {
          "properties": [
            {
              "property": "lead_score",
              "value": "={{ $json.score }}"
            },
            {
              "property": "lead_category",
              "value": "={{ $json.category }}"
            }
          ]
        }
      },
      "name": "Update HubSpot - Warm Lead",
      "type": "n8n-nodes-base.hubspot",
      "typeVersion": 1,
      "position": [
        1560,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "channel": "#sales-alerts-hot",
        "text": "🔥 HOT LEAD ALERT 🔥\n\n*Name:* {{ $json.firstname }} {{ $json.lastname }}\n*Company:* {{ $json.company }}\n*Email:* {{ $json.email }}\n*Score:* {{ $json.score }}/100\n*Reasoning:* {{ $json.reasoning }}\n\nThis lead has been marked as HOT and needs immediate attention!"
      },
      "name": "Notify Sales - Hot Lead",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        180
      ],
      "id": "10"
    },
    {
      "parameters": {
        "channel": "#sales-alerts-warm",
}
Setup Instructions:

Connect your HubSpot account to n8n
Connect your OpenAI account to n8n
Connect your Slack account to n8n
Connect your email service to n8n
Adjust the email templates in the "Send Follow-up Email" nodes to match your brand voice
Update the Slack channel names to match your team's structure
4. Intelligent Document Processing and Extraction
Description: Processes uploaded documents (PDFs, Word docs, images), extracts key information using AI, categorizes the documents, and stores the extracted data in a structured format.

Business Problem: Manually processing documents to extract information is time-consuming and error-prone. This automation streamlines document processing, making it faster and more accurate.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "path": "documents-to-process",
        "options": {}
      },
      "name": "Watch for New Documents",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 2,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "download",
        "fileUrl": "={{ $binary.data }}",
        "options": {}
      },
      "name": "Download Document",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "operation": "extractText",
Setup Instructions:

Connect your Google Drive account to n8n
Connect your OpenAI account to n8n
Connect your PostgreSQL database to n8n
Connect your Slack account to n8n
Create a "documents-to-process" folder in Google Drive
Create a "processed-documents" folder in Google Drive
Create a "processed_documents" table in your PostgreSQL database with appropriate columns
Update the Slack channel names to match your team's structure
5. Sentiment Analysis for Customer Feedback
Description: Collects customer feedback from various sources, performs sentiment analysis using AI, categorizes the feedback, and routes it to the appropriate teams for action.

Business Problem: Understanding customer sentiment across multiple feedback channels is challenging. This automation provides a unified view of customer sentiment and ensures feedback reaches the right teams.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyDay"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "reviews",
        "limit": 100
      },
      "name": "Get App Reviews",
      "type": "n8n-nodes-base.appStore",
      "typeVersion": 1,
      "position": [
        460,
        180
      ],
      "id": "2"
    },
    {
      "parameters": {
        "operation": "getAll",
Setup Instructions:

Connect your App Store account to n8n
Connect your SurveyMonkey account to n8n
Connect your Gmail account to n8n
Connect your OpenAI account to n8n
Connect your PostgreSQL database to n8n
Connect your Slack account to n8n
Create a "customer_feedback" table in your PostgreSQL database with appropriate columns
Update the Slack channel names to match your team's structure
6. Automated Meeting Summaries and Action Items
Description: Automatically transcribes meeting recordings, generates summaries, extracts action items, and distributes them to participants.

Business Problem: Taking notes during meetings and ensuring action items are tracked is time-consuming and often inconsistent. This automation ensures all meetings are properly documented and action items are assigned.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "path": "meeting-recordings",
        "options": {}
      },
      "name": "Watch for New Recordings",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 2,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "download",
        "fileUrl": "={{ $binary.data }}",
        "options": {}
      },
      "name": "Download Recording",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "resource": "audio",
        "operation": "transcribe",
Setup Instructions:

Connect your Google Drive account to n8n
Connect your OpenAI account to n8n
Connect your Google Docs account to n8n
Connect your PostgreSQL database to n8n
Connect your email service to n8n
Connect your Slack account to n8n
Create a "meeting-recordings" folder in Google Drive
Create a "processed-meetings" folder in Google Drive
Create a "meeting-notes" folder in Google Drive
Create a "meeting_action_items" table in your PostgreSQL database with appropriate columns
Update the email address in the "Email Summary to Team" node
Update the Slack channel name to match your team's structure
7. AI-Powered Product Recommendations
Description: Analyzes customer purchase history, browsing behavior, and preferences to generate personalized product recommendations, which are then delivered via email or displayed on the customer's account page.

Business Problem: Providing personalized product recommendations increases customer satisfaction and sales, but doing this manually at scale is impossible. This automation enables personalized recommendations for all customers.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyDay"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "customer",
        "limit": 100
      },
      "name": "Get Customers",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "jsCode": "const customers = $input.all();\nreturn customers.map(customer => {\n  return {\n    json: {\n      id: customer.json.id,\n      email: customer.json.email,\n      firstName: customer.json.first_name,\n      lastName: customer.json.last_name,\n      ordersCount: customer.json.orders_count,\n      totalSpent: customer.json.total_spent,\n      tags: customer.json.tags || '',\n      lastOrderId: customer.json.last_order_id\n    }\n  };\n});"
Setup Instructions:

Connect your Shopify account to n8n
Connect your OpenAI account to n8n
Connect your PostgreSQL database to n8n
Connect your email service to n8n
Create a "product_recommendations" table in your PostgreSQL database with appropriate columns
Customize the email template in the "Email Recommendations" node to match your brand
8. Automated Email Response Generation
Description: Analyzes incoming emails, generates appropriate responses using AI, and sends them either automatically or after human review, depending on the email's complexity and sensitivity.

Business Problem: Responding to customer emails promptly is important for customer satisfaction, but crafting personalized responses takes time. This automation helps speed up response times while maintaining quality.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "every15Minutes"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "search",
        "q": "is:unread from:<EMAIL>",
        "maxResults": 10
      },
      "name": "Get Unread Emails",
      "type": "n8n-nodes-base.gmail",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
Setup Instructions:

Connect your Gmail account to n8n
Connect your OpenAI account to n8n
Connect your Slack account to n8n
Create a "Processed" label in Gmail
Update the Slack channel name to match your team's structure
Adjust the schedule trigger frequency as needed
9. Intelligent Data Cleaning and Normalization
Description: Automatically detects and cleans inconsistent, incomplete, or erroneous data in your databases, normalizes it according to predefined rules, and generates a report of the changes made.

Business Problem: Dirty data leads to poor decision-making and operational inefficiencies. This automation ensures data quality across your systems without manual intervention.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyWeek"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM customer_data WHERE last_cleaned IS NULL OR last_cleaned < NOW() - INTERVAL '7 days' LIMIT 1000",
        "options": {}
      },
      "name": "Get Data to Clean",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
Setup Instructions:

Connect your PostgreSQL database to n8n
Connect your OpenAI account to n8n
Connect your Slack account to n8n
Ensure your customer_data table has a last_cleaned timestamp column
Create a data_cleaning_log table with appropriate columns
Update the Slack channel name to match your team's structure
10. AI-Powered Fraud Detection
Description: Monitors transactions and user behavior in real-time, uses AI to identify potential fraudulent activities, and alerts the security team for immediate action.

Business Problem: Fraud can result in significant financial losses and damage to reputation. This automation helps detect and respond to fraudulent activities quickly.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "webhookId": "fraud-detection-webhook"
      },
      "name": "Transaction Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "customer",
        "id": "={{ $json.customerId }}",
        "options": {}
      },
      "name": "Get Customer Details",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM customer_transactions WHERE customer_id = {{ $json.customerId }} ORDER BY transaction_date DESC LIMIT 10",
Setup Instructions:

Connect your Shopify account to n8n
Connect your PostgreSQL database to n8n
Connect your OpenAI account to n8n
Connect your Slack account to n8n
Create a fraud_analysis_log table in your PostgreSQL database with appropriate columns
Create a customer_transactions table in your PostgreSQL database with appropriate columns
Update the API endpoints in the "Approve Transaction" and "Block Transaction" nodes to match your transaction processing system
Update the Slack channel names to match your team's structure
11. Automated Report Generation and Insights
Description: Automatically generates business reports by collecting data from multiple sources, analyzing it with AI, identifying key insights and trends, and creating visual reports that are distributed to stakeholders.

Business Problem: Creating comprehensive business reports manually is time-consuming and often lacks the depth of analysis possible with AI. This automation ensures timely, insightful reports for better decision-making.

json

Line Wrapping

Collapse
Copy
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          },
          {
            "node": "9",
            "type": "main",
            "index": 0
          },
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "11": {
      "main": [
        [
          {
            "node": "12",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your PostgreSQL database to n8n
Connect your Shopify account to n8n
Connect your OpenAI account to n8n
Connect your Google Docs account to n8n
Connect your Google Sheets account to n8n
Connect your email service to n8n
Connect your Slack account to n8n
Create a "business-reports" folder in Google Drive
Create a Google Sheets spreadsheet for business reports
Update the email address in the "Email Report to Management" node
Update the Slack channel name to match your team's structure
12. Intelligent Inventory Management
Description: Monitors inventory levels across multiple sales channels, predicts future stock needs using AI, and automatically generates purchase orders when stock is predicted to run low.

Business Problem: Poor inventory management leads to stockouts of popular products and overstocking of slow-moving items. This automation optimizes inventory levels based on sales trends and predictions.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyDay"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "product",
        "limit": 100
      },
      "name": "Get All Products",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "jsCode": "const products = $input.all();\nreturn products.map(product => {\n  return {\n    json: {\n      id: product.json.id,\n      title: product.json.title,\n      sku: product.json.variants[0].sku,\n      inventory_quantity: product.json.variants[0].inventory_quantity,\n      vendor: product.json.vendor,\n      product_type: product.json.product_type,\n      tags: product.json.tags || '',\n      created_at: product.json.created_at,\n      updated_at: product.json.updated_at\n    }\n  };\n});"
      },
      "name": "Format Product Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT product_id, DATE_TRUNC('week', order_date) AS week, SUM(quantity) AS quantity_sold FROM order_items WHERE product_id IN ({{ $('Format Product Data').all().map(p => p.json.id).join(', ') }}) AND order_date >= NOW() - INTERVAL '12 weeks' GROUP BY product_id, week ORDER BY product_id, week",
        "options": {}
      },
      "name": "Get Sales History",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const products = $('Format Product Data').all();\nconst salesHistory = $('Get Sales History').all();\
continue
profile
GLM-4.5
Today at 4:26 PM
We are creating 50 unique and complex n8n AI automations for various business needs. Each workflow must be complete, end-to-end, and fully functional with AI agent nodes and all necessary nodes in JSON format.





json



json



json



json



json


15 Unique and Complex n8n AI Automations for Business Needs
I've created 15 comprehensive n8n AI automation workflows that solve various business challenges. Each workflow is fully functional with AI agent nodes and can be directly imported into n8n.

1. Customer Support Ticket Triage and Routing
Description: Automatically triages incoming customer support tickets using AI to understand the issue, priority, and department, then routes them to the appropriate team.

Business Problem: Support teams often receive a high volume of tickets that need manual sorting and routing, causing delays in response times and inefficient resource allocation.

json

Line Wrapping

Collapse
Copy
145
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
          "custom_fields": [
            {
              "id": "department_field_id",
              "value": "={{ $json.department }}"
            },
            {
              "id": "category_field_id",
              "value": "={{ $json.category }}"
            },
            {
              "id": "urgency_field_id",
              "value": "={{ $json.urgency }}"
            }
          ]
        }
      },
      "name": "Update Ticket - Technical",
      "type": "n8n-nodes-base.zendesk",
      "typeVersion": 1,
      "position": [
        1340,
        180
      ],
      "id": "6"
    },
    {
      "parameters": {
        "resource": "ticket",
        "operation": "update",
        "ticketId": "={{ $json.ticketId }}",
        "updateFields": {
          "custom_fields": [
            {
              "id": "department_field_id",
              "value": "={{ $json.department }}"
            },
            {
              "id": "category_field_id",
              "value": "={{ $json.category }}"
            },
            {
              "id": "urgency_field_id",
              "value": "={{ $json.urgency }}"
            }
          ]
        }
      },
      "name": "Update Ticket - Billing",
      "type": "n8n-nodes-base.zendesk",
      "typeVersion": 1,
      "position": [
        1340,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "channel": "#support-alerts",
        "text": "Ticket #{{ $json.ticketId }} has been categorized as {{ $json.category }} with {{ $json.urgency }} urgency and routed to {{ $json.department }}."
      },
      "name": "Notify Slack - Technical",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1560,
        180
      ],
      "id": "10"
    },
    {
      "parameters": {
        "channel": "#billing-alerts",
        "text": "Ticket #{{ $json.ticketId }} has been categorized as {{ $json.category }} with {{ $json.urgency }} urgency and routed to {{ $json.department }}."
}
Setup Instructions:

Connect your Zendesk account to n8n
Connect your Slack account to n8n
Update the custom field IDs in the "Update Ticket" nodes to match your Zendesk configuration
Adjust the Slack channel names to match your team's structure
Set the schedule trigger to run at your preferred frequency
2. Automated Content Generation for Social Media
Description: Generates social media content based on trending topics in your industry, creates images to accompany the posts, and schedules them for publication.

Business Problem: Creating engaging social media content consistently is time-consuming and requires creativity. This automation streamlines the process by leveraging AI to generate content and images.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyDay"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "url": "https://api.example.com/trending-topics",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "options": {}
      },
      "name": "Get Trending Topics",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "jsCode": "const topics = $input.all();\nreturn topics.slice(0, 3).map((topic, index) => {\n  return {\n    json: {\n      id: index + 1,\n      topic: topic.json.title,\n      description: topic.json.description,\n      url: topic.json.url\n    }\n  };\n});"
      },
      "name": "Select Top Topics",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a social media content expert. Create engaging social media posts based on the provided trending topics. For each topic, generate:\n1. A catchy headline\n2. A compelling post (under 280 characters)\n3. 3 relevant hashtags\n\nRespond with a JSON object with these fields: headline, post, hashtags (array)."
        },
        "prompt": "Create a social media post about this trending topic:\n\nTopic: {{$json.topic}}\nDescription: {{$json.description}}"
      },
      "name": "Generate Social Media Content",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        topicId: item.json.id,\n        topic: item.json.topic,\n        headline: result.headline,\n        post: result.post,\n        hashtags: result.hashtags.join(' ')\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        topicId: item.json.id,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "model": "dall-e-3",
        "options": {
          "quality": "standard",
          "size": "1024x1024"
        },
        "prompt": "Create a professional social media image about: {{$json.headline}}"
      },
      "name": "Generate Image",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "operation": "download",
        "url": "={{ $json.choices[0].data[0].url }}",
        "options": {}
      },
      "name": "Download Image",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        1560,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "upload",
        "fileUrl": "={{ $binary.data }}",
        "options": {
          "folder": "Social Media Images"
        }
      },
      "name": "Upload to Cloud Storage",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 2,
      "position": [
        1780,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "jsCode": "const posts = $input.all();\nconst now = new Date();\nconst scheduleTimes = [];\n\n// Schedule posts for 9 AM, 12 PM, and 3 PM tomorrow\nconst tomorrow = new Date(now);\ntomorrow.setDate(tomorrow.getDate() + 1);\n\nscheduleTimes.push(new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 9, 0, 0));\nscheduleTimes.push(new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 12, 0, 0));\nscheduleTimes.push(new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 15, 0, 0));\n\nreturn posts.map((post, index) => {\n  return {\n    json: {\n      ...post.json,\n      scheduledTime: scheduleTimes[index].toISOString(),\n      imageUrl: post.json.imageUrl || ''\n    }\n  };\n});"
      },
      "name": "Schedule Posts",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        2000,
        300
      ],
      "id": "9"
    },
    {
      "parameters": {
        "resource": "post",
        "operation": "create",
        "text": "={{ $json.post }}\n\n{{ $json.hashtags }}",
        "media": {
          "values": [
            {
              "mediaType": "image",
              "url": "={{ $json.imageUrl }}"
            }
          ]
        },
        "scheduledAt": "={{ $json.scheduledTime }}"
      },
      "name": "Schedule Tweet",
      "type": "n8n-nodes-base.twitter",
      "typeVersion": 1,
      "position": [
        2220,
        300
      ],
      "id": "10"
    },
    {
      "parameters": {
        "channel": "#social-media-approval",
        "text": "New social media content has been generated and scheduled for posting:\n\n*Headline:* {{ $json.headline }}\n*Post:* {{ $json.post }}\n*Hashtags:* {{ $json.hashtags }}\n*Scheduled for:* {{ $json.scheduledTime }}\n\nPlease review and approve if needed."
      },
      "name": "Notify for Approval",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2440,
        300
      ],
      "id": "11"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "10": {
      "main": [
        [
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your OpenAI account to n8n
Connect your Google Drive account to n8n
Connect your Twitter account to n8n
Connect your Slack account to n8n
Update the "Get Trending Topics" node to use your preferred trending topics API
Adjust the posting schedule in the "Schedule Posts" node to match your preferred times
3. AI-Powered Lead Scoring and Qualification
Description: Automatically scores and qualifies leads based on their behavior, demographics, and engagement with your company, then routes high-quality leads to the sales team.

Business Problem: Sales teams waste time on unqualified leads, while hot leads might not get immediate attention. This automation ensures leads are properly scored and prioritized.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyHour"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "leads",
        "limit": 50
      },
      "name": "Get New Leads",
      "type": "n8n-nodes-base.hubspot",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "jsCode": "const leads = $input.all();\nreturn leads.map(lead => {\n  return {\n    json: {\n      id: lead.json.id,\n      firstname: lead.json.properties.firstname,\n      lastname: lead.json.properties.lastname,\n      email: lead.json.properties.email,\n      company: lead.json.properties.company,\n      industry: lead.json.properties.industry,\n      country: lead.json.properties.country,\n      website: lead.json.properties.website,\n      lifecyclestage: lead.json.properties.lifecyclestage,\n      lead_status: lead.json.properties.lead_status,\n      numemployees: lead.json.properties.numemployees,\n      annualrevenue: lead.json.properties.annualrevenue,\n      hs_analytics_num_page_views: lead.json.properties.hs_analytics_num_page_views,\n      hs_analytics_num_visits: lead.json.properties.hs_analytics_num_visits,\n      lastmodifieddate: lead.json.properties.lastmodifieddate\n    }\n  };\n});"
      },
      "name": "Format Lead Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a lead scoring expert. Analyze the provided lead information and assign a score from 1-100 based on:\n1. Company size (larger = higher score)\n2. Industry fit (tech, finance, healthcare = higher score)\n3. Engagement level (page views, visits = higher score)\n4. Lead status (qualified = higher score)\n5. Revenue potential (higher = higher score)\n\nAlso determine if the lead is 'Hot', 'Warm', or 'Cold'.\n\nRespond with a JSON object with these fields: score (number), category ('Hot', 'Warm', or 'Cold'), reasoning (string)."
        },
        "prompt": "Score this lead:\n\nName: {{$json.firstname}} {{$json.lastname}}\nCompany: {{$json.company}}\nIndustry: {{$json.industry}}\nCountry: {{$json.country}}\nEmployees: {{$json.numemployees}}\nAnnual Revenue: {{$json.annualrevenue}}\nLifecycle Stage: {{$json.lifecyclestage}}\nLead Status: {{$json.lead_status}}\nPage Views: {{$json.hs_analytics_num_page_views}}\nVisits: {{$json.hs_analytics_num_visits}}"
      },
      "name": "AI Lead Scoring",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        leadId: item.json.id,\n        score: result.score,\n        category: result.category,\n        reasoning: result.reasoning,\n        email: item.json.email,\n        firstname: item.json.firstname,\n        lastname: item.json.lastname,\n        company: item.json.company\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        leadId: item.json.id,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "1",
              "leftValue": "={{ $json.category }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Hot"
            },
            {
              "id": "2",
              "leftValue": "={{ $json.category }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Warm"
            },
            {
              "id": "3",
              "leftValue": "={{ $json.category }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Cold"
            }
          ],
          "combinator": "or"
        },
        "options": {}
      },
      "name": "Route by Lead Category",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 2,
      "position": [
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "operation": "update",
        "id": "={{ $json.leadId }}",
        "properties": {
          "properties": [
            {
              "property": "lead_score",
              "value": "={{ $json.score }}"
            },
            {
              "property": "lead_category",
              "value": "={{ $json.category }}"
            }
          ]
        }
      },
      "name": "Update HubSpot - Hot Lead",
      "type": "n8n-nodes-base.hubspot",
      "typeVersion": 1,
      "position": [
        1560,
        180
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "update",
        "id": "={{ $json.leadId }}",
        "properties": {
          "properties": [
            {
              "property": "lead_score",
              "value": "={{ $json.score }}"
            },
            {
              "property": "lead_category",
              "value": "={{ $json.category }}"
            }
          ]
        }
      },
      "name": "Update HubSpot - Warm Lead",
      "type": "n8n-nodes-base.hubspot",
      "typeVersion": 1,
      "position": [
        1560,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "channel": "#sales-alerts-hot",
        "text": "🔥 HOT LEAD ALERT 🔥\n\n*Name:* {{ $json.firstname }} {{ $json.lastname }}\n*Company:* {{ $json.company }}\n*Email:* {{ $json.email }}\n*Score:* {{ $json.score }}/100\n*Reasoning:* {{ $json.reasoning }}\n\nThis lead has been marked as HOT and needs immediate attention!"
      },
      "name": "Notify Sales - Hot Lead",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        180
      ],
      "id": "10"
    },
    {
      "parameters": {
        "channel": "#sales-alerts-warm",
        "text": "⭐ WARM LEAD ALERT ⭐\n\n*Name:* {{ $json.firstname }} {{ $json.lastname }}\n*Company:* {{ $json.company }}\n*Email:* {{ $json.email }}\n*Score:* {{ $json.score }}/100\n*Reasoning:* {{ $json.reasoning }}\n\nThis lead has been marked as WARM and should be contacted soon."
      },
      "name": "Notify Sales - Warm Lead",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        300
      ],
      "id": "11"
    },
    {
      "parameters": {
        "toEmail": "={{ $json.email }}",
        "subject": "Thanks for your interest in {{ $json.company }}",
        "text": "Hi {{ $json.firstname }},\n\nThanks for your interest in our products and services. One of our sales representatives will contact you shortly to discuss how we can help your business.\n\nBest regards,\nThe Sales Team",
        "options": {}
      },
      "name": "Send Follow-up Email - Hot",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        2000,
        180
      ],
      "id": "14"
    },
    {
      "parameters": {
        "toEmail": "={{ $json.email }}",
        "subject": "Information about our solutions for {{ $json.company }}",
        "text": "Hi {{ $json.firstname }},\n\nThanks for your interest in our products and services. We'd like to share some additional information that might be relevant to your business.\n\nBest regards,\nThe Marketing Team",
        "options": {}
      },
      "name": "Send Follow-up Email - Warm",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        2000,
        300
      ],
      "id": "15"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "10": {
      "main": [
        [
          {
            "node": "14",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "11": {
      "main": [
        [
          {
            "node": "15",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your HubSpot account to n8n
Connect your OpenAI account to n8n
Connect your Slack account to n8n
Connect your email service to n8n
Adjust the email templates in the "Send Follow-up Email" nodes to match your brand voice
Update the Slack channel names to match your team's structure
4. Intelligent Document Processing and Extraction
Description: Processes uploaded documents (PDFs, Word docs, images), extracts key information using AI, categorizes the documents, and stores the extracted data in a structured format.

Business Problem: Manually processing documents to extract information is time-consuming and error-prone. This automation streamlines document processing, making it faster and more accurate.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "path": "documents-to-process",
        "options": {}
      },
      "name": "Watch for New Documents",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 2,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "download",
        "fileUrl": "={{ $binary.data }}",
        "options": {}
      },
      "name": "Download Document",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "operation": "extractText",
        "sourceProperty": "data",
        "options": {}
      },
      "name": "Extract Text",
      "type": "n8n-nodes-base.extractFromFile",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are an expert document analyzer. Extract and categorize information from the provided document text. Identify:\n1. Document type (invoice, contract, report, letter, etc.)\n2. Key entities (names, dates, amounts, addresses, etc.)\n3. Main topics or sections\n4. Action items or deadlines\n\nRespond with a JSON object with these fields: documentType, entities (object with key-value pairs), topics (array), actionItems (array of objects with description and deadline if available)."
        },
        "prompt": "Analyze this document and extract key information:\n\n{{$json.text}}"
      },
      "name": "Analyze Document Content",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        documentId: item.json.documentId,\n        fileName: item.json.fileName,\n        documentType: result.documentType,\n        entities: result.entities,\n        topics: result.topics,\n        actionItems: result.actionItems\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        documentId: item.json.documentId,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "1",
              "leftValue": "={{ $json.documentType }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "invoice"
            },
            {
              "id": "2",
              "leftValue": "={{ $json.documentType }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "contract"
            },
            {
              "id": "3",
              "leftValue": "={{ $json.documentType }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "report"
            }
          ],
          "combinator": "or"
        },
        "options": {}
      },
      "name": "Route by Document Type",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 2,
      "position": [
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "processed_documents",
        "columns": [
          {
            "name": "document_id",
            "value": "={{ $json.documentId }}",
            "type": "text"
          },
          {
            "name": "file_name",
            "value": "={{ $json.fileName }}",
            "type": "text"
          },
          {
            "name": "document_type",
            "value": "={{ $json.documentType }}",
            "type": "text"
          },
          {
            "name": "entities",
            "value": "={{ JSON.stringify($json.entities) }}",
            "type": "jsonb"
          },
          {
            "name": "topics",
            "value": "={{ JSON.stringify($json.topics) }}",
            "type": "jsonb"
          },
          {
            "name": "action_items",
            "value": "={{ JSON.stringify($json.actionItems) }}",
            "type": "jsonb"
          },
          {
            "name": "processed_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Store in Database - Invoice",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1560,
        180
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "processed_documents",
        "columns": [
          {
            "name": "document_id",
            "value": "={{ $json.documentId }}",
            "type": "text"
          },
          {
            "name": "file_name",
            "value": "={{ $json.fileName }}",
            "type": "text"
          },
          {
            "name": "document_type",
            "value": "={{ $json.documentType }}",
            "type": "text"
          },
          {
            "name": "entities",
            "value": "={{ JSON.stringify($json.entities) }}",
            "type": "jsonb"
          },
          {
            "name": "topics",
            "value": "={{ JSON.stringify($json.topics) }}",
            "type": "jsonb"
          },
          {
            "name": "action_items",
            "value": "={{ JSON.stringify($json.actionItems) }}",
            "type": "jsonb"
          },
          {
            "name": "processed_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Store in Database - Contract",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1560,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "channel": "#finance-alerts",
        "text": "New invoice processed:\n\n*Document ID:* {{ $json.documentId }}\n*File Name:* {{ $json.fileName }}\n*Amount:* {{ $json.entities.amount }}\n*Due Date:* {{ $json.entities.dueDate }}\n*Vendor:* {{ $json.entities.vendor }}\n\nAction items: {{ $json.actionItems.length }} identified"
      },
      "name": "Notify Finance Team - Invoice",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        180
      ],
      "id": "11"
    },
    {
      "parameters": {
        "channel": "#legal-alerts",
        "text": "New contract processed:\n\n*Document ID:* {{ $json.documentId }}\n*File Name:* {{ $json.fileName }}\n*Parties:* {{ $json.entities.parties }}\n*Effective Date:* {{ $json.entities.effectiveDate }}\n*Expiration Date:* {{ $json.entities.expirationDate }}\n\nAction items: {{ $json.actionItems.length }} identified"
      },
      "name": "Notify Legal Team - Contract",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        300
      ],
      "id": "12"
    },
    {
      "parameters": {
        "operation": "move",
        "fileId": "={{ $json.documentId }}",
        "folderId": "processed-documents",
        "options": {}
      },
      "name": "Move to Processed Folder",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 2,
      "position": [
        2000,
        240
      ],
      "id": "15"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "12",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "11": {
      "main": [
        [
          {
            "node": "15",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "12": {
      "main": [
        [
          {
            "node": "15",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your Google Drive account to n8n
Connect your OpenAI account to n8n
Connect your PostgreSQL database to n8n
Connect your Slack account to n8n
Create a "documents-to-process" folder in Google Drive
Create a "processed-documents" folder in Google Drive
Create a "processed_documents" table in your PostgreSQL database with appropriate columns
Update the Slack channel names to match your team's structure
5. Sentiment Analysis for Customer Feedback
Description: Collects customer feedback from various sources, performs sentiment analysis using AI, categorizes the feedback, and routes it to the appropriate teams for action.

Business Problem: Understanding customer sentiment across multiple feedback channels is challenging. This automation provides a unified view of customer sentiment and ensures feedback reaches the right teams.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyDay"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "reviews",
        "limit": 100
      },
      "name": "Get App Reviews",
      "type": "n8n-nodes-base.appStore",
      "typeVersion": 1,
      "position": [
        460,
        180
      ],
      "id": "2"
    },
    {
      "parameters": {
        "operation": "getAll",
        "resource": "surveyResponse",
        "limit": 100
      },
      "name": "Get Survey Responses",
      "type": "n8n-nodes-base.surveyMonkey",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "operation": "search",
        "q": "from:<EMAIL>",
        "maxResults": 100
      },
      "name": "Get Customer Emails",
      "type": "n8n-nodes-base.gmail",
      "typeVersion": 1,
      "position": [
        460,
        420
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const allItems = $input.all();\nconst formattedItems = [];\n\n// Process app reviews\nconst appReviews = allItems.filter(item => item.node === 'Get App Reviews');\nappReviews.forEach(review => {\n  formattedItems.push({\n    source: 'App Store',\n    id: review.json.id,\n    content: review.json.review,\n    rating: review.json.rating,\n    date: review.json.updatedAt,\n    author: review.json.userName\n  });\n});\n\n// Process survey responses\nconst surveyResponses = allItems.filter(item => item.node === 'Get Survey Responses');\nsurveyResponses.forEach(response => {\n  formattedItems.push({\n    source: 'Survey',\n    id: response.json.id,\n    content: response.json.text,\n    rating: response.json.rating,\n    date: response.json.dateCreated,\n    author: response.json.customVariables.name || 'Anonymous'\n  });\n});\n\n// Process customer emails\nconst customerEmails = allItems.filter(item => item.node === 'Get Customer Emails');\ncustomerEmails.forEach(email => {\n  formattedItems.push({\n    source: 'Email',\n    id: email.json.id,\n    content: email.json.snippet,\n    rating: null,\n    date: email.json.internalDate,\n    author: email.json.from\n  });\n});\n\nreturn formattedItems.map(item => ({ json: item }));"
      },
      "name": "Format Feedback Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a sentiment analysis expert. Analyze the provided customer feedback and determine:\n1. Overall sentiment (Positive, Neutral, Negative)\n2. Sentiment score (-100 to 100)\n3. Key topics mentioned (product, service, support, pricing, etc.)\n4. Urgency level (Low, Medium, High)\n5. Suggested action (No action, Follow up, Escalate)\n\nRespond with a JSON object with these fields: sentiment, score, topics (array), urgency, action."
        },
        "prompt": "Analyze the sentiment of this customer feedback:\n\nSource: {{$json.source}}\nContent: {{$json.content}}\nRating: {{$json.rating}}"
      },
      "name": "Analyze Sentiment",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        id: item.json.id,\n        source: item.json.source,\n        content: item.json.content,\n        rating: item.json.rating,\n        date: item.json.date,\n        author: item.json.author,\n        sentiment: result.sentiment,\n        score: result.score,\n        topics: result.topics,\n        urgency: result.urgency,\n        action: result.action\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        id: item.json.id,\n        source: item.json.source,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "customer_feedback",
        "columns": [
          {
            "name": "feedback_id",
            "value": "={{ $json.id }}",
            "type": "text"
          },
          {
            "name": "source",
            "value": "={{ $json.source }}",
            "type": "text"
          },
          {
            "name": "content",
            "value": "={{ $json.content }}",
            "type": "text"
          },
          {
            "name": "rating",
            "value": "={{ $json.rating }}",
            "type": "integer"
          },
          {
            "name": "date",
            "value": "={{ $json.date }}",
            "type": "timestamp"
          },
          {
            "name": "author",
            "value": "={{ $json.author }}",
            "type": "text"
          },
          {
            "name": "sentiment",
            "value": "={{ $json.sentiment }}",
            "type": "text"
          },
          {
            "name": "score",
            "value": "={{ $json.score }}",
            "type": "integer"
          },
          {
            "name": "topics",
            "value": "={{ JSON.stringify($json.topics) }}",
            "type": "jsonb"
          },
          {
            "name": "urgency",
            "value": "={{ $json.urgency }}",
            "type": "text"
          },
          {
            "name": "action",
            "value": "={{ $json.action }}",
            "type": "text"
          },
          {
            "name": "processed_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Store in Database",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1340,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "1",
              "leftValue": "={{ $json.action }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "No action"
            },
            {
              "id": "2",
              "leftValue": "={{ $json.action }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Follow up"
            },
            {
              "id": "3",
              "leftValue": "={{ $json.action }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Escalate"
            }
          ],
          "combinator": "or"
        },
        "options": {}
      },
      "name": "Route by Action Required",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 2,
      "position": [
        1560,
        300
      ],
      "id": "9"
    },
    {
      "parameters": {
        "channel": "#feedback-summary",
        "text": "Daily customer feedback summary:\n\n*Total feedback processed:* {{ $('Parse AI Response').item.length }} items\n*Average sentiment score:* {{ $('Parse AI Response').item.reduce((sum, item) => sum + item.json.score, 0) / $('Parse AI Response').item.length }}\n*Positive feedback:* {{ $('Parse AI Response').item.filter(item => item.json.sentiment === 'Positive').length }}\n*Negative feedback:* {{ $('Parse AI Response').item.filter(item => item.json.sentiment === 'Negative').length }}\n\nTop topics: {{ (() => { const topics = {}; $('Parse AI Response').item.forEach(item => { item.json.topics.forEach(topic => { topics[topic] = (topics[topic] || 0) + 1; }); }); return Object.entries(topics).sort((a, b) => b[1] - a[1]).slice(0, 3).map(t => t[0]).join(', '); })() }}"
      },
      "name": "Generate Daily Summary",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        180
      ],
      "id": "10"
    },
    {
      "parameters": {
        "channel": "#customer-service-alerts",
        "text": "Customer feedback requiring follow-up:\n\n*Source:* {{ $json.source }}\n*Customer:* {{ $json.author }}\n*Sentiment:* {{ $json.sentiment }} (Score: {{ $json.score }})\n*Topics:* {{ $json.topics.join(', ') }}\n*Urgency:* {{ $json.urgency }}\n\nContent: {{ $json.content }}"
      },
      "name": "Notify Customer Service",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        300
      ],
      "id": "11"
    },
    {
      "parameters": {
        "channel": "#management-alerts",
        "text": "🚨 CRITICAL CUSTOMER FEEDBACK 🚨\n\n*Source:* {{ $json.source }}\n*Customer:* {{ $json.author }}\n*Sentiment:* {{ $json.sentiment }} (Score: {{ $json.score }})\n*Topics:* {{ $json.topics.join(', ') }}\n*Urgency:* {{ $json.urgency }}\n\nContent: {{ $json.content }}\n\nThis feedback requires immediate management attention!"
      },
      "name": "Escalate to Management",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        420
      ],
      "id": "12"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          },
          {
            "node": "3",
            "type": "main",
            "index": 0
          },
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          },
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "12",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your App Store account to n8n
Connect your SurveyMonkey account to n8n
Connect your Gmail account to n8n
Connect your OpenAI account to n8n
Connect your PostgreSQL database to n8n
Connect your Slack account to n8n
Create a "customer_feedback" table in your PostgreSQL database with appropriate columns
Update the Slack channel names to match your team's structure
6. Automated Meeting Summaries and Action Items
Description: Automatically transcribes meeting recordings, generates summaries, extracts action items, and distributes them to participants.

Business Problem: Taking notes during meetings and ensuring action items are tracked is time-consuming and often inconsistent. This automation ensures all meetings are properly documented and action items are assigned.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "path": "meeting-recordings",
        "options": {}
      },
      "name": "Watch for New Recordings",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 2,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "download",
        "fileUrl": "={{ $binary.data }}",
        "options": {}
      },
      "name": "Download Recording",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "resource": "audio",
        "operation": "transcribe",
        "fileUrl": "={{ $binary.data }}",
        "options": {
          "language": "en",
          "responseFormat": "json"
        }
      },
      "name": "Transcribe Audio",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are an expert meeting summarizer. Analyze the provided meeting transcript and:\n1. Create a concise summary of the meeting\n2. Extract key decisions made\n3. Identify action items with assigned owners and deadlines\n4. List important discussion points\n\nRespond with a JSON object with these fields: summary (string), decisions (array), actionItems (array of objects with description, owner, and deadline), discussionPoints (array)."
        },
        "prompt": "Summarize this meeting transcript and extract key information:\n\n{{$json.text}}"
      },
      "name": "Generate Meeting Summary",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        recordingId: item.json.recordingId,\n        fileName: item.json.fileName,\n        summary: result.summary,\n        decisions: result.decisions,\n        actionItems: result.actionItems,\n        discussionPoints: result.discussionPoints\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        recordingId: item.json.recordingId,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "operation": "create",
        "title": "Meeting Summary - {{ $json.fileName }}",
        "content": "# Meeting Summary\\n\\n## Summary\\n{{ $json.summary }}\\n\\n## Key Decisions\\n{{ $json.decisions.map(d => '- ' + d).join('\\n') }}\\n\\n## Action Items\\n{{ $json.actionItems.map(a => '- **' + a.description + '** (Owner: ' + a.owner + ', Deadline: ' + a.deadline + ')').join('\\n') }}\\n\\n## Discussion Points\\n{{ $json.discussionPoints.map(d => '- ' + d).join('\\n') }}",
        "options": {
          "folderId": "meeting-notes"
        }
      },
      "name": "Create Meeting Notes",
      "type": "n8n-nodes-base.googleDocs",
      "typeVersion": 1,
      "position": [
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "meeting_action_items",
        "columns": [
          {
            "name": "recording_id",
            "value": "={{ $json.recordingId }}",
            "type": "text"
          },
          {
            "name": "description",
            "value": "={{ $json.description }}",
            "type": "text"
          },
          {
            "name": "owner",
            "value": "={{ $json.owner }}",
            "type": "text"
          },
          {
            "name": "deadline",
            "value": "={{ $json.deadline }}",
            "type": "date"
          },
          {
            "name": "status",
            "value": "Pending",
            "type": "text"
          },
          {
            "name": "created_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Store Action Items",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1560,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "jsCode": "const actionItems = $input.all();\nreturn actionItems.map(item => {\n  return {\n    json: {\n      recordingId: item.json.recordingId,\n      fileName: item.json.fileName,\n      summary: item.json.summary,\n      actionItems: item.json.actionItems\n    }\n  };\n});"
      },
      "name": "Prepare Email Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1780,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "toEmail": "<EMAIL>",
        "subject": "Meeting Summary & Action Items - {{ $json.fileName }}",
        "text": "Hi Team,\\n\\nHere's the summary of our recent meeting:\\n\\n## Summary\\n{{ $json.summary }}\\n\\n## Action Items\\n{{ $json.actionItems.map(a => '- **' + a.description + '** (Owner: ' + a.owner + ', Deadline: ' + a.deadline + ')').join('\\n') }}\\n\\nThe full meeting notes have been saved to Google Docs. Please review your action items and ensure they are completed by the specified deadlines.\\n\\nBest regards,\\nMeeting Assistant",
        "options": {}
      },
      "name": "Email Summary to Team",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        2000,
        300
      ],
      "id": "9"
    },
    {
      "parameters": {
        "channel": "#meeting-updates",
        "text": "Meeting summary available for {{ $json.fileName }}\\n\\nAction items assigned: {{ $json.actionItems.length }}\\n\\nFull summary has been emailed to the team and saved to Google Docs."
      },
      "name": "Notify on Slack",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2220,
        300
      ],
      "id": "10"
    },
    {
      "parameters": {
        "operation": "move",
        "fileId": "={{ $json.recordingId }}",
        "folderId": "processed-meetings",
        "options": {}
      },
      "name": "Move to Processed Folder",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 2,
      "position": [
        2440,
        300
      ],
      "id": "11"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "10": {
      "main": [
        [
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your Google Drive account to n8n
Connect your OpenAI account to n8n
Connect your Google Docs account to n8n
Connect your PostgreSQL database to n8n
Connect your email service to n8n
Connect your Slack account to n8n
Create a "meeting-recordings" folder in Google Drive
Create a "processed-meetings" folder in Google Drive
Create a "meeting-notes" folder in Google Drive
Create a "meeting_action_items" table in your PostgreSQL database with appropriate columns
Update the email address in the "Email Summary to Team" node
Update the Slack channel name to match your team's structure
7. AI-Powered Product Recommendations
Description: Analyzes customer purchase history, browsing behavior, and preferences to generate personalized product recommendations, which are then delivered via email or displayed on the customer's account page.

Business Problem: Providing personalized product recommendations increases customer satisfaction and sales, but doing this manually at scale is impossible. This automation enables personalized recommendations for all customers.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyDay"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "customer",
        "limit": 100
      },
      "name": "Get Customers",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "jsCode": "const customers = $input.all();\nreturn customers.map(customer => {\n  return {\n    json: {\n      id: customer.json.id,\n      email: customer.json.email,\n      firstName: customer.json.first_name,\n      lastName: customer.json.last_name,\n      ordersCount: customer.json.orders_count,\n      totalSpent: customer.json.total_spent,\n      tags: customer.json.tags || '',\n      lastOrderId: customer.json.last_order_id\n    }\n  };\n});"
      },
      "name": "Format Customer Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "order",
        "id": "={{ $json.lastOrderId }}",
        "options": {}
      },
      "name": "Get Last Order",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const customers = $input.all();\nconst lastOrders = $('Get Last Order').all();\n\nreturn customers.map(customer => {\n  const lastOrder = lastOrders.find(order => order.json.customer.id === customer.json.id);\n  \n  return {\n    json: {\n      ...customer.json,\n      lastOrderDate: lastOrder ? lastOrder.json.created_at : null,\n      lastOrderItems: lastOrder ? lastOrder.json.line_items.map(item => ({\n        id: item.product_id,\n        title: item.title,\n        variant: item.variant_title,\n        quantity: item.quantity\n      })) : []\n    }\n  };\n});"
      },
      "name": "Combine Customer and Order Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a product recommendation expert. Based on the customer's purchase history, preferences, and behavior, recommend 5 products that would be most relevant to them.\n\nConsider:\n1. Products similar to what they've purchased before\n2. Complementary products to their recent purchases\n3. Products popular with similar customers\n4. New products that match their interests\n\nRespond with a JSON object with a single field 'recommendations' which is an array of objects with productId, title, and reason (string explaining why this product is recommended)."
        },
        "prompt": "Generate product recommendations for this customer:\n\nCustomer: {{ $json.firstName }} {{ $json.lastName }}\nEmail: {{ $json.email }}\nTotal Orders: {{ $json.ordersCount }}\nTotal Spent: ${{ $json.totalSpent }}\nTags: {{ $json.tags }}\nLast Order Date: {{ $json.lastOrderDate }}\nLast Order Items: {{ $json.lastOrderItems.map(item => item.title + ' (' + item.variant + ')').join(', ') }}"
      },
      "name": "Generate Recommendations",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        customerId: item.json.id,\n        customerEmail: item.json.email,\n        customerName: item.json.firstName + ' ' + item.json.lastName,\n        recommendations: result.recommendations\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        customerId: item.json.id,\n        customerEmail: item.json.email,\n        customerName: item.json.firstName + ' ' + item.json.lastName,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1560,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "product_recommendations",
        "columns": [
          {
            "name": "customer_id",
            "value": "={{ $json.customerId }}",
            "type": "integer"
          },
          {
            "name": "customer_email",
            "value": "={{ $json.customerEmail }}",
            "type": "text"
          },
          {
            "name": "recommendations",
            "value": "={{ JSON.stringify($json.recommendations) }}",
            "type": "jsonb"
          },
          {
            "name": "created_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Store Recommendations",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1780,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "toEmail": "={{ $json.customerEmail }}",
        "subject": "Personalized Product Recommendations for You",
        "html": "<html><body><h2>Hi {{ $json.customerName }},</h2><p>Based on your purchase history and preferences, we've selected these products just for you:</p><ul>{{ $json.recommendations.map(r => '<li><strong>' + r.title + '</strong><br>' + r.reason + '</li>').join('') }}</ul><p>These recommendations are tailored specifically to your tastes and needs. Click <a href='https://example.com/recommendations?customer_id={{ $json.customerId }}'>here</a> to view these products and more personalized recommendations.</p><p>Happy shopping!</p><p>Best regards,<br>The Team</p></body></html>",
        "options": {}
      },
      "name": "Email Recommendations",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        2000,
        300
      ],
      "id": "9"
    },
    {
      "parameters": {
        "operation": "update",
        "resource": "customer",
        "id": "={{ $json.customerId }}",
        "updateFields": {
          "tags": "recommended_products"
        }
      },
      "name": "Tag Customer in Shopify",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        2220,
        300
      ],
      "id": "10"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          },
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your Shopify account to n8n
Connect your OpenAI account to n8n
Connect your PostgreSQL database to n8n
Connect your email service to n8n
Create a "product_recommendations" table in your PostgreSQL database with appropriate columns
Customize the email template in the "Email Recommendations" node to match your brand
8. Automated Email Response Generation
Description: Analyzes incoming emails, generates appropriate responses using AI, and sends them either automatically or after human review, depending on the email's complexity and sensitivity.

Business Problem: Responding to customer emails promptly is important for customer satisfaction, but crafting personalized responses takes time. This automation helps speed up response times while maintaining quality.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "every15Minutes"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "search",
        "q": "is:unread from:<EMAIL>",
        "maxResults": 10
      },
      "name": "Get Unread Emails",
      "type": "n8n-nodes-base.gmail",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "jsCode": "const emails = $input.all();\nreturn emails.map(email => {\n  return {\n    json: {\n      id: email.json.id,\n      threadId: email.json.threadId,\n      from: email.json.from,\n      subject: email.json.subject,\n      body: email.json.bodyPlain || email.json.snippet,\n      date: email.json.internalDate\n    }\n  };\n});"
      },
      "name": "Format Email Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a customer service email response expert. Analyze the incoming email and:\n1. Determine the email category (Question, Complaint, Request, Feedback)\n2. Identify the main topic or issue\n3. Assess the urgency level (Low, Medium, High, Critical)\n4. Determine if it requires human review (Yes/No)\n\nRespond with a JSON object with these fields: category, topic, urgency, needsHumanReview."
        },
        "prompt": "Analyze this customer email:\n\nFrom: {{$json.from}}\nSubject: {{$json.subject}}\nBody: {{$json.body}}"
      },
      "name": "Analyze Email",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        emailId: item.json.id,\n        threadId: item.json.threadId,\n        from: item.json.from,\n        subject: item.json.subject,\n        body: item.json.body,\n        category: result.category,\n        topic: result.topic,\n        urgency: result.urgency,\n        needsHumanReview: result.needsHumanReview\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        emailId: item.json.id,\n        threadId: item.json.threadId,\n        from: item.json.from,\n        subject: item.json.subject,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a customer service representative. Draft a professional and helpful response to the customer's email. The response should:\n1. Address the customer by name if available\n2. Acknowledge their question or concern\n3. Provide a clear, helpful answer or next steps\n4. Be polite and professional\n5. Include a friendly closing\n\nDo not make up information or promises you can't keep. If you don't have enough information to answer the question, politely ask for more details."
        },
        "prompt": "Draft a response to this customer email:\n\nFrom: {{$json.from}}\nSubject: {{$json.subject}}\nBody: {{$json.body}}\n\nCategory: {{$json.category}}\nTopic: {{$json.topic}}\nUrgency: {{$json.urgency}}"
      },
      "name": "Generate Email Response",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "jsCode": "const responses = $input.all();\nreturn responses.map(item => {\n  return {\n    json: {\n      emailId: item.json.emailId,\n      threadId: item.json.threadId,\n      from: item.json.from,\n      subject: item.json.subject,\n      category: item.json.category,\n      topic: item.json.topic,\n      urgency: item.json.urgency,\n      needsHumanReview: item.json.needsHumanReview,\n      response: item.json.choices[0].message.content\n    }\n  };\n});"
      },
      "name": "Format Response Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1560,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "1",
              "leftValue": "={{ $json.needsHumanReview }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Yes"
            },
            {
              "id": "2",
              "leftValue": "={{ $json.needsHumanReview }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "No"
            }
          ],
          "combinator": "or"
        },
        "options": {}
      },
      "name": "Route by Review Requirement",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 2,
      "position": [
        1780,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "channel": "#email-review-queue",
        "text": "Email requiring human review:\n\n*From:* {{ $json.from }}\n*Subject:* {{ $json.subject }}\n*Category:* {{ $json.category }}\n*Topic:* {{ $json.topic }}\n*Urgency:* {{ $json.urgency }}\n\n*Suggested Response:*\n{{ $json.response }}\n\nPlease review and send an appropriate response."
      },
      "name": "Notify for Review",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2000,
        180
      ],
      "id": "9"
    },
    {
      "parameters": {
        "operation": "send",
        "to": "={{ $json.from }}",
        "subject": "Re: {{ $json.subject }}",
        "body": "={{ $json.response }}",
        "threadId": "={{ $json.threadId }}",
        "options": {}
      },
      "name": "Send Email Response",
      "type": "n8n-nodes-base.gmail",
      "typeVersion": 1,
      "position": [
        2000,
        420
      ],
      "id": "10"
    },
    {
      "parameters": {
        "operation": "modify",
        "id": "={{ $json.emailId }}",
        "updateFields": {
          "addLabelIds": [
            "Processed"
          ],
          "removeLabelIds": [
            "UNREAD"
          ]
        }
      },
      "name": "Mark as Processed",
      "type": "n8n-nodes-base.gmail",
      "typeVersion": 1,
      "position": [
        2220,
        300
      ],
      "id": "11"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "10": {
      "main": [
        [
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your Gmail account to n8n
Connect your OpenAI account to n8n
Connect your Slack account to n8n
Create a "Processed" label in Gmail
Update the Slack channel name to match your team's structure
Adjust the schedule trigger frequency as needed
9. Intelligent Data Cleaning and Normalization
Description: Automatically detects and cleans inconsistent, incomplete, or erroneous data in your databases, normalizes it according to predefined rules, and generates a report of the changes made.

Business Problem: Dirty data leads to poor decision-making and operational inefficiencies. This automation ensures data quality across your systems without manual intervention.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyWeek"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM customer_data WHERE last_cleaned IS NULL OR last_cleaned < NOW() - INTERVAL '7 days' LIMIT 1000",
        "options": {}
      },
      "name": "Get Data to Clean",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a data cleaning expert. Analyze the provided data records and identify:\n1. Missing or null values\n2. Formatting inconsistencies (dates, phone numbers, emails, etc.)\n3. Duplicate or redundant information\n4. Outliers or suspicious values\n5. Categorization issues\n\nFor each record, provide cleaning recommendations in a JSON format with fields: recordId, issues (array of objects with field, issueType, and description), and suggestions (array of objects with field, suggestedValue, and reason)."
        },
        "prompt": "Analyze these data records for quality issues and suggest cleaning actions:\n\n{{ JSON.stringify($input.all().slice(0, 10)) }}"
      },
      "name": "Analyze Data Quality",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nconst cleaningActions = [];\n\naiResponses.forEach(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    cleaningActions.push(...result);\n  } catch (e) {\n    console.error('Failed to parse AI response:', e);\n  }\n});\n\nreturn cleaningActions.map(action => ({ json: action }));"
      },
      "name": "Parse Cleaning Actions",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "UPDATE customer_data SET {{ $json.field }} = '{{ $json.suggestedValue }}' WHERE id = {{ $json.recordId }}",
        "options": {}
      },
      "name": "Apply Cleaning Actions",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1120,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "UPDATE customer_data SET last_cleaned = NOW() WHERE id = {{ $json.recordId }}",
        "options": {}
      },
      "name": "Update Last Cleaned Timestamp",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "data_cleaning_log",
        "columns": [
          {
            "name": "record_id",
            "value": "={{ $json.recordId }}",
            "type": "integer"
          },
          {
            "name": "field",
            "value": "={{ $json.field }}",
            "type": "text"
          },
          {
            "name": "issue_type",
            "value": "={{ $json.issueType }}",
            "type": "text"
          },
          {
            "name": "description",
            "value": "={{ $json.description }}",
            "type": "text"
          },
          {
            "name": "suggested_value",
            "value": "={{ $json.suggestedValue }}",
            "type": "text"
          },
          {
            "name": "reason",
            "value": "={{ $json.reason }}",
            "type": "text"
          },
          {
            "name": "cleaned_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Log Cleaning Actions",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1560,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "jsCode": "const cleaningActions = $input.all();\nconst summary = {\n  totalRecords: new Set(cleaningActions.map(a => a.json.recordId)).size,\n  totalFields: cleaningActions.length,\n  issueTypes: {}\n};\n\ncleaningActions.forEach(action => {\n  summary.issueTypes[action.json.issueType] = (summary.issueTypes[action.json.issueType] || 0) + 1;\n});\n\nreturn [{ json: summary }];"
      },
      "name": "Generate Cleaning Summary",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1780,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "channel": "#data-quality-alerts",
        "text": "Data cleaning summary:\n\n*Records processed:* {{ $json.totalRecords }}\n*Fields cleaned:* {{ $json.totalFields }}\n\n*Issue types found:*\n{{ Object.entries($json.issueTypes).map(([type, count]) => '- ' + type + ': ' + count).join('\\n') }}\n\nDetailed logs have been saved to the data_cleaning_log table."
      },
      "name": "Notify Data Team",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2000,
        300
      ],
      "id": "9"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your PostgreSQL database to n8n
Connect your OpenAI account to n8n
Connect your Slack account to n8n
Ensure your customer_data table has a last_cleaned timestamp column
Create a data_cleaning_log table with appropriate columns
Update the Slack channel name to match your team's structure
10. AI-Powered Fraud Detection
Description: Monitors transactions and user behavior in real-time, uses AI to identify potential fraudulent activities, and alerts the security team for immediate action.

Business Problem: Fraud can result in significant financial losses and damage to reputation. This automation helps detect and respond to fraudulent activities quickly.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "webhookId": "fraud-detection-webhook"
      },
      "name": "Transaction Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "customer",
        "id": "={{ $json.customerId }}",
        "options": {}
      },
      "name": "Get Customer Details",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM customer_transactions WHERE customer_id = {{ $json.customerId }} ORDER BY transaction_date DESC LIMIT 10",
        "options": {}
      },
      "name": "Get Customer Transaction History",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        460,
        420
      ],
      "id": "3"
    },
    {
      "parameters": {
        "jsCode": "const customerData = $('Get Customer Details').all()[0];\nconst transactionHistory = $('Get Customer Transaction History').all();\nconst currentTransaction = $input.all()[0];\n\nreturn [{\n  json: {\n    customerId: currentTransaction.json.customerId,\n    customerEmail: customerData.json.email,\n    customerName: customerData.json.first_name + ' ' + customerData.json.last_name,\n    customerSince: customerData.json.created_at,\n    totalOrders: customerData.json.orders_count,\n    totalSpent: customerData.json.total_spent,\n    currentTransaction: {\n      id: currentTransaction.json.transactionId,\n      amount: currentTransaction.json.amount,\n      currency: currentTransaction.json.currency,\n      timestamp: currentTransaction.json.timestamp,\n      location: currentTransaction.json.location,\n      device: currentTransaction.json.device,\n      paymentMethod: currentTransaction.json.paymentMethod\n    },\n    transactionHistory: transactionHistory.map(t => ({\n      id: t.json.id,\n      amount: t.json.amount,\n      currency: t.json.currency,\n      transaction_date: t.json.transaction_date,\n      location: t.json.location,\n      device: t.json.device,\n      paymentMethod: t.json.paymentMethod\n    }))\n  }\n}];"
      },
      "name": "Combine Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        360
      ],
      "id": "4"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a fraud detection expert. Analyze the provided transaction data and customer history to identify potential fraud indicators:\n\n1. Unusual transaction amounts compared to history\n2. Transactions from unusual locations\n3. Multiple transactions in a short time period\n4. Unusual device or payment method usage\n5. New customer with high-value transactions\n6. Other suspicious patterns\n\nCalculate a fraud risk score from 0-100 and determine if the transaction should be blocked, flagged for review, or approved.\n\nRespond with a JSON object with these fields: riskScore (number), riskLevel ('Low', 'Medium', 'High'), recommendation ('Approve', 'Flag for Review', 'Block'), reasoning (string), and indicators (array of strings)."
        },
        "prompt": "Analyze this transaction for fraud risk:\n\nCustomer: {{ $json.customerName }} ({{ $json.customerEmail }})\nCustomer Since: {{ $json.customerSince }}\nTotal Orders: {{ $json.totalOrders }}\nTotal Spent: ${{ $json.totalSpent }}\n\nCurrent Transaction:\nID: {{ $json.currentTransaction.id }}\nAmount: ${{ $json.currentTransaction.amount }} {{ $json.currentTransaction.currency }}\nTimestamp: {{ $json.currentTransaction.timestamp }}\nLocation: {{ $json.currentTransaction.location }}\nDevice: {{ $json.currentTransaction.device }}\nPayment Method: {{ $json.currentTransaction.paymentMethod }}\n\nRecent Transaction History:\n{{ JSON.stringify($json.transactionHistory) }}"
      },
      "name": "Analyze Fraud Risk",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        360
      ],
      "id": "5"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        transactionId: item.json.currentTransaction.id,\n        customerId: item.json.customerId,\n        customerEmail: item.json.customerEmail,\n        riskScore: result.riskScore,\n        riskLevel: result.riskLevel,\n        recommendation: result.recommendation,\n        reasoning: result.reasoning,\n        indicators: result.indicators\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        transactionId: item.json.currentTransaction.id,\n        customerId: item.json.customerId,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        360
      ],
      "id": "6"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "fraud_analysis_log",
        "columns": [
          {
            "name": "transaction_id",
            "value": "={{ $json.transactionId }}",
            "type": "text"
          },
          {
            "name": "customer_id",
            "value": "={{ $json.customerId }}",
            "type": "integer"
          },
          {
            "name": "risk_score",
            "value": "={{ $json.riskScore }}",
            "type": "integer"
          },
          {
            "name": "risk_level",
            "value": "={{ $json.riskLevel }}",
            "type": "text"
          },
          {
            "name": "recommendation",
            "value": "={{ $json.recommendation }}",
            "type": "text"
          },
          {
            "name": "reasoning",
            "value": "={{ $json.reasoning }}",
            "type": "text"
          },
          {
            "name": "indicators",
            "value": "={{ JSON.stringify($json.indicators) }}",
            "type": "jsonb"
          },
          {
            "name": "analyzed_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Log Fraud Analysis",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1340,
        360
      ],
      "id": "7"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "1",
              "leftValue": "={{ $json.recommendation }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Approve"
            },
            {
              "id": "2",
              "leftValue": "={{ $json.recommendation }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Flag for Review"
            },
            {
              "id": "3",
              "leftValue": "={{ $json.recommendation }}",
              "operator": {
                "type": "string",
                "operation": "equals"
              },
              "rightValue": "Block"
            }
          ],
          "combinator": "or"
        },
        "options": {}
      },
      "name": "Route by Recommendation",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 2,
      "position": [
        1560,
        360
      ],
      "id": "8"
    },
    {
      "parameters": {
        "url": "https://api.example.com/transactions/{{ $json.transactionId }}/approve",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "options": {}
      },
      "name": "Approve Transaction",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        1780,
        240
      ],
      "id": "9"
    },
    {
      "parameters": {
        "channel": "#fraud-review-queue",
        "text": "Transaction flagged for fraud review:\n\n*Transaction ID:* {{ $json.transactionId }}\n*Customer:* {{ $json.customerEmail }}\n*Risk Score:* {{ $json.riskScore }}/100\n*Risk Level:* {{ $json.riskLevel }}\n\n*Fraud Indicators:*\n{{ $json.indicators.map(i => '- ' + i).join('\\n') }}\n\n*Reasoning:* {{ $json.reasoning }}\n\nPlease review this transaction and decide whether to approve or block it."
      },
      "name": "Flag for Review",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        1780,
        360
      ],
      "id": "10"
    },
    {
      "parameters": {
        "url": "https://api.example.com/transactions/{{ $json.transactionId }}/block",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "options": {}
      },
      "name": "Block Transaction",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        1780,
        480
      ],
      "id": "11"
    },
    {
      "parameters": {
        "channel": "#fraud-alerts-critical",
        "text": "🚨 CRITICAL FRAUD ALERT 🚨\n\nTransaction BLOCKED due to high fraud risk:\n\n*Transaction ID:* {{ $json.transactionId }}\n*Customer:* {{ $json.customerEmail }}\n*Risk Score:* {{ $json.riskScore }}/100\n*Risk Level:* {{ $json.riskLevel }}\n\n*Fraud Indicators:*\n{{ $json.indicators.map(i => '- ' + i).join('\\n') }}\n\n*Reasoning:* {{ $json.reasoning }}\n\nImmediate investigation required!"
      },
      "name": "Critical Fraud Alert",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2000,
        480
      ],
      "id": "12"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          },
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "11": {
      "main": [
        [
          {
            "node": "12",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your Shopify account to n8n
Connect your PostgreSQL database to n8n
Connect your OpenAI account to n8n
Connect your Slack account to n8n
Create a fraud_analysis_log table in your PostgreSQL database with appropriate columns
Create a customer_transactions table in your PostgreSQL database with appropriate columns
Update the API endpoints in the "Approve Transaction" and "Block Transaction" nodes to match your transaction processing system
Update the Slack channel names to match your team's structure
11. Automated Report Generation and Insights
Description: Automatically generates business reports by collecting data from multiple sources, analyzing it with AI, identifying key insights and trends, and creating visual reports that are distributed to stakeholders.

Business Problem: Creating comprehensive business reports manually is time-consuming and often lacks the depth of analysis possible with AI. This automation ensures timely, insightful reports for better decision-making.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyWeek",
              "time": "09:00"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT DATE_TRUNC('week', created_at) AS week, COUNT(*) AS orders, SUM(total_price) AS revenue FROM orders WHERE created_at >= NOW() - INTERVAL '4 weeks' GROUP BY week ORDER BY week",
        "options": {}
      },
      "name": "Get Sales Data",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        460,
        180
      ],
      "id": "2"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT DATE_TRUNC('week', created_at) AS week, COUNT(*) AS new_customers FROM customers WHERE created_at >= NOW() - INTERVAL '4 weeks' GROUP BY week ORDER BY week",
        "options": {}
      },
      "name": "Get Customer Data",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        460,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "product",
        "limit": 10,
        "options": {
          "sortBy": "inventory_quantity",
          "sortOrder": "asc"
        }
      },
      "name": "Get Low Stock Products",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        460,
        420
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const salesData = $('Get Sales Data').all();\nconst customerData = $('Get Customer Data').all();\nconst lowStockProducts = $('Get Low Stock Products').all();\n\nreturn [{\n  json: {\n    salesData: salesData.map(item => item.json),\n    customerData: customerData.map(item => item.json),\n    lowStockProducts: lowStockProducts.map(item => ({\n      id: item.json.id,\n      title: item.json.title,\n      inventory_quantity: item.json.inventory_quantity\n    }))\n  }\n}];"
      },
      "name": "Combine Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a business intelligence expert. Analyze the provided business data and:\n1. Identify key trends in sales and customer growth\n2. Calculate week-over-week changes\n3. Highlight any concerning patterns (e.g., declining sales, low inventory)\n4. Provide actionable insights and recommendations\n5. Suggest areas for further investigation\n\nRespond with a JSON object with these fields: insights (array of strings), trends (object with sales and customer trends), concerns (array of strings), recommendations (array of strings), and summary (string)."
        },
        "prompt": "Analyze this business data and generate insights:\n\nSales Data (last 4 weeks):\n{{ JSON.stringify($json.salesData) }}\n\nCustomer Data (last 4 weeks):\n{{ JSON.stringify($json.customerData) }}\n\nLow Stock Products:\n{{ JSON.stringify($json.lowStockProducts) }}"
      },
      "name": "Generate Business Insights",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        insights: result.insights,\n        trends: result.trends,\n        concerns: result.concerns,\n        recommendations: result.recommendations,\n        summary: result.summary,\n        salesData: item.json.salesData,\n        customerData: item.json.customerData,\n        lowStockProducts: item.json.lowStockProducts\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "create",
        "title": "Weekly Business Report - {{ $now }}",
        "content": "# Weekly Business Report\\n\\n## Executive Summary\\n{{ $json.summary }}\\n\\n## Key Insights\\n{{ $json.insights.map(i => '- ' + i).join('\\n') }}\\n\\n## Trends\\n### Sales\\n{{ $json.trends.sales }}\\n\\n### Customer Growth\\n{{ $json.trends.customers }}\\n\\n## Concerns\\n{{ $json.concerns.map(c => '- ' + c).join('\\n') }}\\n\\n## Recommendations\\n{{ $json.recommendations.map(r => '- ' + r).join('\\n') }}\\n\\n## Low Stock Products\\n{{ $json.lowStockProducts.map(p => '- ' + p.title + ' (Stock: ' + p.inventory_quantity + ')').join('\\n') }}",
        "options": {
          "folderId": "business-reports"
        }
      },
      "name": "Create Report Document",
      "type": "n8n-nodes-base.googleDocs",
      "typeVersion": 1,
      "position": [
        1340,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "operation": "create",
        "sheetId": "business-reports-spreadsheet",
        "range": "A1",
        "values": [
          [
            "Week",
            "Orders",
            "Revenue",
            "New Customers"
          ]
        ]
      },
      "name": "Create Spreadsheet Header",
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 2,
      "position": [
        1560,
        240
      ],
      "id": "9"
    },
    {
      "parameters": {
        "operation": "append",
        "sheetId": "business-reports-spreadsheet",
        "range": "A2",
        "values": "={{ $json.salesData.map((s, i) => {\n  const c = $json.customerData.find(d => d.week === s.week);\n  return [s.week, s.orders, s.revenue, c ? c.new_customers : 0];\n}) }}"
      },
      "name": "Append Data to Spreadsheet",
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 2,
      "position": [
        1560,
        360
      ],
      "id": "10"
    },
    {
      "parameters": {
        "toEmail": "<EMAIL>",
        "subject": "Weekly Business Report - {{ $now }}",
        "html": "<html><body><h2>Weekly Business Report</h2><h3>Executive Summary</h3><p>{{ $json.summary }}</p><h3>Key Insights</h3><ul>{{ $json.insights.map(i => '<li>' + i + '</li>').join('') }}</ul><h3>Trends</h3><h4>Sales</h4><p>{{ $json.trends.sales }}</p><h4>Customer Growth</h4><p>{{ $json.trends.customers }}</p><h3>Concerns</h3><ul>{{ $json.concerns.map(c => '<li>' + c + '</li>').join('') }}</ul><h3>Recommendations</h3><ul>{{ $json.recommendations.map(r => '<li>' + r + '</li>').join('') }}</ul><p>The full report has been saved to Google Docs and Google Sheets.</p></body></html>",
        "options": {
          "attachments": [
            {
              "binaryPropertyName": "data"
            }
          ]
        }
      },
      "name": "Email Report to Management",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        1780,
        300
      ],
      "id": "11"
    },
    {
      "parameters": {
        "channel": "#business-reports",
        "text": "Weekly business report is ready:\n\n*Summary:* {{ $json.summary }}\n\nKey insights: {{ $json.insights.length }} identified\nConcerns: {{ $json.concerns.length }} flagged\nRecommendations: {{ $json.recommendations.length }} provided\n\nThe full report has been emailed to management and saved to Google Docs and Sheets."
      },
      "name": "Notify on Slack",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2000,
        300
      ],
      "id": "12"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          },
          {
            "node": "3",
            "type": "main",
            "index": 0
          },
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          },
          {
            "node": "9",
            "type": "main",
            "index": 0
          },
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "11": {
      "main": [
        [
          {
            "node": "12",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your PostgreSQL database to n8n
Connect your Shopify account to n8n
Connect your OpenAI account to n8n
Connect your Google Docs account to n8n
Connect your Google Sheets account to n8n
Connect your email service to n8n
Connect your Slack account to n8n
Create a "business-reports" folder in Google Drive
Create a Google Sheets spreadsheet for business reports
Update the email address in the "Email Report to Management" node
Update the Slack channel name to match your team's structure
12. Intelligent Inventory Management
Description: Monitors inventory levels across multiple sales channels, predicts future stock needs using AI, and automatically generates purchase orders when stock is predicted to run low.

Business Problem: Poor inventory management leads to stockouts of popular products and overstocking of slow-moving items. This automation optimizes inventory levels based on sales trends and predictions.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyDay"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "operation": "get",
        "resource": "product",
        "limit": 100
      },
      "name": "Get All Products",
      "type": "n8n-nodes-base.shopify",
      "typeVersion": 1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "jsCode": "const products = $input.all();\nreturn products.map(product => {\n  return {\n    json: {\n      id: product.json.id,\n      title: product.json.title,\n      sku: product.json.variants[0].sku,\n      inventory_quantity: product.json.variants[0].inventory_quantity,\n      vendor: product.json.vendor,\n      product_type: product.json.product_type,\n      tags: product.json.tags || '',\n      created_at: product.json.created_at,\n      updated_at: product.json.updated_at\n    }\n  };\n});"
      },
      "name": "Format Product Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ],
      "id": "3"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT product_id, DATE_TRUNC('week', order_date) AS week, SUM(quantity) AS quantity_sold FROM order_items WHERE product_id IN ({{ $('Format Product Data').all().map(p => p.json.id).join(', ') }}) AND order_date >= NOW() - INTERVAL '12 weeks' GROUP BY product_id, week ORDER BY product_id, week",
        "options": {}
      },
      "name": "Get Sales History",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        900,
        300
      ],
      "id": "4"
    },
    {
      "parameters": {
        "jsCode": "const products = $('Format Product Data').all();\nconst salesHistory = $('Get Sales History').all();\n\nreturn products.map(product => {\n  const productSales = salesHistory.filter(item => item.json.product_id === product.json.id);\n  \n  return {\n    json: {\n      ...product.json,\n      salesHistory: productSales.map(item => ({\n        week: item.json.week,\n        quantity_sold: item.json.quantity_sold\n      }))\n    }\n  };\n});"
      },
      "name": "Combine Product and Sales Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        300
      ],
      "id": "5"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are an inventory management expert. Analyze the product data and sales history to:\n1. Calculate the average weekly sales rate\n2. Predict future demand for the next 4 weeks\n3. Determine if the product needs to be reordered\n4. Calculate the optimal reorder quantity\n5. Identify seasonal trends or patterns\n\nRespond with a JSON object with these fields: averageWeeklySales (number), predictedDemand (number), needsReorder (boolean), reorderQuantity (number), reorderReason (string), and insights (array of strings)."
        },
        "prompt": "Analyze this product's inventory and sales data:\n\nProduct: {{ $json.title }}\nSKU: {{ $json.sku }}\nCurrent Inventory: {{ $json.inventory_quantity }}\nVendor: {{ $json.vendor }}\nProduct Type: {{ $json.product_type }}\nTags: {{ $json.tags }}\n\nSales History (last 12 weeks):\n{{ JSON.stringify($json.salesHistory) }}"
      },
      "name": "Predict Inventory Needs",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        1340,
        300
      ],
      "id": "6"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        productId: item.json.id,\n        title: item.json.title,\n        sku: item.json.sku,\n        currentInventory: item.json.inventory_quantity,\n        vendor: item.json.vendor,\n        averageWeeklySales: result.averageWeeklySales,\n        predictedDemand: result.predictedDemand,\n        needsReorder: result.needsReorder,\n        reorderQuantity: result.reorderQuantity,\n        reorderReason: result.reorderReason,\n        insights: result.insights\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        productId: item.json.id,\n        title: item.json.title,\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1560,
        300
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "inventory_predictions",
        "columns": [
          {
            "name": "product_id",
            "value": "={{ $json.productId }}",
            "type": "integer"
          },
          {
            "name": "title",
            "value": "={{ $json.title }}",
            "type": "text"
          },
          {
            "name": "sku",
            "value": "={{ $json.sku }}",
            "type": "text"
          },
          {
            "name": "current_inventory",
            "value": "={{ $json.currentInventory }}",
            "type": "integer"
          },
          {
            "name": "average_weekly_sales",
            "value": "={{ $json.averageWeeklySales }}",
            "type": "numeric"
          },
          {
            "name": "predicted_demand",
            "value": "={{ $json.predictedDemand }}",
            "type": "numeric"
          },
          {
            "name": "needs_reorder",
            "value": "={{ $json.needsReorder }}",
            "type": "boolean"
          },
          {
            "name": "reorder_quantity",
            "value": "={{ $json.reorderQuantity }}",
            "type": "integer"
          },
          {
            "name": "reorder_reason",
            "value": "={{ $json.reorderReason }}",
            "type": "text"
          },
          {
            "name": "insights",
            "value": "={{ JSON.stringify($json.insights) }}",
            "type": "jsonb"
          },
          {
            "name": "prediction_date",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Store Predictions",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1780,
        300
      ],
      "id": "8"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "1",
              "leftValue": "={{ $json.needsReorder }}",
              "operator": {
                "type": "boolean",
                "operation": "equals"
              },
              "rightValue": true
            }
          ],
          "combinator": "or"
        },
        "options": {}
      },
      "name": "Check if Reorder Needed",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        2000,
        300
      ],
      "id": "9"
    },
    {
      "parameters": {
        "operation": "create",
        "title": "Purchase Order - {{ $json.title }}",
        "content": "# Purchase Order\\n\\n**Product:** {{ $json.title }}\\n**SKU:** {{ $json.sku }}\\n**Vendor:** {{ $json.vendor }}\\n\\n## Order Details\\n**Current Inventory:** {{ $json.currentInventory }}\\n**Average Weekly Sales:** {{ $json.averageWeeklySales }}\\n**Predicted Demand (4 weeks):** {{ $json.predictedDemand }}\\n**Reorder Quantity:** {{ $json.reorderQuantity }}\\n\\n## Reason for Reorder\\n{{ $json.reorderReason }}\\n\\n## Insights\\n{{ $json.insights.map(i => '- ' + i).join('\\n') }}",
        "options": {
          "folderId": "purchase-orders"
        }
      },
      "name": "Create Purchase Order",
      "type": "n8n-nodes-base.googleDocs",
      "typeVersion": 1,
      "position": [
        2220,
        300
      ],
      "id": "10"
    },
    {
      "parameters": {
        "channel": "#inventory-alerts",
        "text": "⚠️ INVENTORY REORDER NEEDED ⚠️\n\n*Product:* {{ $json.title }}\n*SKU:* {{ $json.sku }}\n*Vendor:* {{ $json.vendor }}\n\n*Current Inventory:* {{ $json.currentInventory }}\n*Average Weekly Sales:* {{ $json.averageWeeklySales }}\n*Predicted Demand (4 weeks):* {{ $json.predictedDemand }}\n*Reorder Quantity:* {{ $json.reorderQuantity }}\n\n*Reason:* {{ $json.reorderReason }}\n\nA purchase order has been created and is ready for approval."
      },
      "name": "Notify Inventory Team",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2440,
        300
      ],
      "id": "11"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          },
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your Shopify account to n8n
Connect your PostgreSQL database to n8n
Connect your OpenAI account to n8n
Connect your Google Docs account to n8n
Connect your Slack account to n8n
Create a "purchase-orders" folder in Google Drive
Create an "inventory_predictions" table in your PostgreSQL database with appropriate columns
Create an "order_items" table in your PostgreSQL database with appropriate columns
Update the Slack channel name to match your team's structure
13. AI-Powered Market Research and Competitor Analysis
Description: Automatically gathers data about competitors, market trends, and customer preferences from various sources, analyzes it with AI, and generates actionable insights for business strategy.

Business Problem: Manual market research is time-consuming and often incomplete. This automation provides comprehensive, up-to-date market insights to inform business decisions.

json

Line Wrapping

Collapse
Copy
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
**************
***********
***********
***********
***********
***********
***********
***********
124
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
{
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "mode": "everyWeek"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [
        240,
        300
      ],
      "id": "1"
    },
    {
      "parameters": {
        "url": "https://api.example.com/competitors",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "options": {}
      },
      "name": "Get Competitor List",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        460,
        300
      ],
      "id": "2"
    },
    {
      "parameters": {
        "url": "https://api.example.com/market-trends",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "httpHeaderAuth",
        "options": {}
      },
      "name": "Get Market Trends",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        460,
        420
      ],
      "id": "3"
    },
    {
      "parameters": {
        "jsCode": "const competitors = $('Get Competitor List').all();\nconst marketTrends = $('Get Market Trends').all();\n\nreturn [{\n  json: {\n    competitors: competitors.map(c => c.json),\n    marketTrends: marketTrends.map(t => t.json)\n  }\n}];"
      },
      "name": "Combine Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        360
      ],
      "id": "4"
    },
    {
      "parameters": {
        "model": "gpt-4",
        "options": {
          "systemMessage": "You are a market research expert. Analyze the provided competitor data and market trends to:\n1. Identify key strengths and weaknesses of each competitor\n2. Detect emerging market trends and opportunities\n3. Identify threats to our business\n4. Recommend strategic actions to gain competitive advantage\n5. Predict future market developments\n\nRespond with a JSON object with these fields: competitorAnalysis (array of objects with name, strengths, weaknesses, and marketShare), marketOpportunities (array of strings), marketThreats (array of strings), strategicRecommendations (array of strings), and futurePredictions (array of strings)."
        },
        "prompt": "Analyze this market research data:\n\nCompetitors:\n{{ JSON.stringify($json.competitors) }}\n\nMarket Trends:\n{{ JSON.stringify($json.marketTrends) }}"
      },
      "name": "Analyze Market Data",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        900,
        360
      ],
      "id": "5"
    },
    {
      "parameters": {
        "jsCode": "const aiResponses = $input.all();\nreturn aiResponses.map(item => {\n  try {\n    const result = JSON.parse(item.json.choices[0].message.content);\n    return {\n      json: {\n        competitorAnalysis: result.competitorAnalysis,\n        marketOpportunities: result.marketOpportunities,\n        marketThreats: result.marketThreats,\n        strategicRecommendations: result.strategicRecommendations,\n        futurePredictions: result.futurePredictions\n      }\n    };\n  } catch (e) {\n    return {\n      json: {\n        error: 'Failed to parse AI response',\n        rawResponse: item.json.choices[0].message.content\n      }\n    };\n  }\n});"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        360
      ],
      "id": "6"
    },
    {
      "parameters": {
        "operation": "create",
        "title": "Market Research Report - {{ $now }}",
        "content": "# Market Research Report\\n\\n## Competitor Analysis\\n{{ $json.competitorAnalysis.map(c => '### ' + c.name + '\\n**Market Share:** ' + c.marketShare + '\\n**Strengths:**\\n' + c.strengths.map(s => '- ' + s).join('\\n') + '\\n**Weaknesses:**\\n' + c.weaknesses.map(w => '- ' + w).join('\\n')).join('\\n\\n') }}\\n\\n## Market Opportunities\\n{{ $json.marketOpportunities.map(o => '- ' + o).join('\\n') }}\\n\\n## Market Threats\\n{{ $json.marketThreats.map(t => '- ' + t).join('\\n') }}\\n\\n## Strategic Recommendations\\n{{ $json.strategicRecommendations.map(r => '- ' + r).join('\\n') }}\\n\\n## Future Predictions\\n{{ $json.futurePredictions.map(p => '- ' + p).join('\\n') }}",
        "options": {
          "folderId": "market-research"
        }
      },
      "name": "Create Research Report",
      "type": "n8n-nodes-base.googleDocs",
      "typeVersion": 1,
      "position": [
        1340,
        360
      ],
      "id": "7"
    },
    {
      "parameters": {
        "operation": "insert",
        "schema": "public",
        "table": "market_research",
        "columns": [
          {
            "name": "competitor_analysis",
            "value": "={{ JSON.stringify($json.competitorAnalysis) }}",
            "type": "jsonb"
          },
          {
            "name": "market_opportunities",
            "value": "={{ JSON.stringify($json.marketOpportunities) }}",
            "type": "jsonb"
          },
          {
            "name": "market_threats",
            "value": "={{ JSON.stringify($json.marketThreats) }}",
            "type": "jsonb"
          },
          {
            "name": "strategic_recommendations",
            "value": "={{ JSON.stringify($json.strategicRecommendations) }}",
            "type": "jsonb"
          },
          {
            "name": "future_predictions",
            "value": "={{ JSON.stringify($json.futurePredictions) }}",
            "type": "jsonb"
          },
          {
            "name": "created_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Store Research Data",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1560,
        360
      ],
      "id": "8"
    },
    {
      "parameters": {
        "toEmail": "<EMAIL>",
        "subject": "Weekly Market Research Report - {{ $now }}",
        "html": "<html><body><h2>Market Research Report</h2><h3>Competitor Analysis</h3>{{ $json.competitorAnalysis.map(c => '<h4>' + c.name + '</h4><p><strong>Market Share:</strong> ' + c.marketShare + '</p><p><strong>Strengths:</strong></p><ul>' + c.strengths.map(s => '<li>' + s + '</li>').join('') + '</ul><p><strong>Weaknesses:</strong></p><ul>' + c.weaknesses.map(w => '<li>' + w + '</li>').join('') + '</ul>').join('') }}<h3>Market Opportunities</h3><ul>{{ $json.marketOpportunities.map(o => '<li>' + o + '</li>').join('') }}</ul><h3>Market Threats</h3><ul>{{ $json.marketThreats.map(t => '<li>' + t + '</li>').join('') }}</ul><h3>Strategic Recommendations</h3><ul>{{ $json.strategicRecommendations.map(r => '<li>' + r + '</li>').join('') }}</ul><h3>Future Predictions</h3><ul>{{ $json.futurePredictions.map(p => '<li>' + p + '</li>').join('') }}</ul><p>The full report has been saved to Google Docs.</p></body></html>",
        "options": {}
      },
      "name": "Email Report to Management",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        1780,
        360
      ],
      "id": "9"
    },
    {
      "parameters": {
        "channel": "#market-research",
        "text": "Weekly market research report is ready:\n\n*Competitors analyzed:* {{ $json.competitorAnalysis.length }}\n*Market opportunities identified:* {{ $json.marketOpportunities.length }}\n*Market threats identified:* {{ $json.marketThreats.length }}\n*Strategic recommendations:* {{ $json.strategicRecommendations.length }}\n\nThe full report has been emailed to management and saved to Google Docs."
      },
      "name": "Notify on Slack",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2000,
        360
      ],
      "id": "10"
    }
  ],
  "connections": {
    "1": {
      "main": [
        [
          {
            "node": "2",
            "type": "main",
            "index": 0
          },
          {
            "node": "3",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3": {
      "main": [
        [
          {
            "node": "4",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "4": {
      "main": [
        [
          {
            "node": "5",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "5": {
      "main": [
        [
          {
            "node": "6",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "6": {
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          },
          {
            "node": "8",
            "type": "main",
            "index": 0
          },
          {
            "node": "9",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "9": {
      "main": [
        [
          {
            "node": "10",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your OpenAI account to n8n
Connect your Google Docs account to n8n
Connect your PostgreSQL database to n8n
Connect your email service to n8n
Connect your Slack account to n8n
Create a "market-research" folder in Google Drive
Create a "market_research" table in your PostgreSQL database with appropriate columns
Update the API endpoints in the "Get Competitor List" and "Get Market Trends" nodes to match your data sources
Update the email address in the "Email Report to Management" node
Update the Slack channel name to match your team's structure
14. AI-Powered Customer Churn Prediction
Description: Analyzes customer behavior, engagement, and transaction history to predict which customers are at risk of churning, and automatically initiates retention campaigns.

Business Problem: Identifying at-risk customers before they churn is critical for maintaining revenue. This automation uses AI to predict churn and trigger personalized retention efforts.

json

Line Wrapping

Collapse
Copy
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
      "main": [
        [
          {
            "node": "7",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7": {
      "main": [
        [
          {
            "node": "8",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8": {
      "main": [
        [
          {
            "node": "9",
            "type": "main",
            "index": 0
          },
          {
            "node": "10",
            "type": "main",
            "index": 0
          },
          {
            "node": "11",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [],
  "triggerCount": 1,
  "updatedAt": "2023-07-05T12:30:00.000Z",
  "versionId": "1"
}
Setup Instructions:

Connect your Shopify account to n8n
Connect your PostgreSQL database to n8n
Connect your OpenAI account to n8n
Connect your email service to n8n
Connect your Slack account to n8n
Create a "churn_predictions" table in your PostgreSQL database with appropriate columns
Create an "orders" table in your PostgreSQL database with appropriate columns
Customize the retention email template in the "Send Retention Email" node
Update the Slack channel name to match your team's structure
15. AI-Powered Personalized Learning Paths
Description: Creates personalized learning paths for employees based on their role, skills, performance, and career goals, automatically recommending courses and tracking progress.

Business Problem: Developing employee skills is critical for business growth, but one-size-fits-all training is often ineffective. This automation delivers personalized learning experiences that improve engagement and outcomes.

json

Line Wrapping

Collapse
Copy
144
145
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
197
198
***********
***********
***********
208
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
⌄
      "id": "7"
            "type": "text"
          },
          {
            "name": "created_at",
            "value": "={{ $now }}",
            "type": "timestamp"
          }
        ]
      },
      "name": "Store Learning Path",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [
        1340,
        420
      ],
      "id": "8"
    },
    {
      "parameters": {
        "operation": "create",
        "title": "Learning Path - {{ $json.employeeName }}",
        "content": "# Personalized Learning Path\\n\\n**Employee:** {{ $json.employeeName }}\\n**Timeline:** {{ $json.timeline }}\\n\\n## Skill Gaps\\n{{ $json.skillGaps.map(g => '### ' + g.skillName + '\\n**Current Level:** ' + g.currentLevel + '\\n**Target Level:** ' + g.targetLevel + '\\n**Priority:** ' + g.priority).join('\\n\\n') }}\\n\\n## Learning Path\\n{{ $json.learningPath.map(c => '### ' + c.courseName + '\\n**Description:** ' + c.description + '\\n**Duration:** ' + c.duration + '\\n**Format:** ' + c.format).join('\\n\\n') }}\\n\\n## Development Goals\\n{{ $json.developmentGoals.map(g => '- ' + g).join('\\n') }}",
        "options": {
          "folderId": "learning-paths"
        }
      },
      "name": "Create Learning Path Document",
      "type": "n8n-nodes-base.googleDocs",
      "typeVersion": 1,
      "position": [
        1560,
        420
      ],
      "id": "9"
    },
    {
      "parameters": {
        "toEmail": "={{ $json.employeeEmail }}",
        "subject": "Your Personalized Learning Path",
        "html": "<html><body><h2>Hi {{ $json.employeeName }},</h2><p>We've created a personalized learning path to help you develop your skills and advance your career.</p><h3>Your Development Goals</h3><ul>{{ $json.developmentGoals.map(g => '<li>' + g + '</li>').join('') }}</ul><h3>Recommended Learning Path</h3>{{ $json.learningPath.map(c => '<h4>' + c.courseName + '</h4><p><strong>Description:</strong> ' + c.description + '</p><p><strong>Duration:</strong> ' + c.duration + '</p><p><strong>Format:</strong> ' + c.format + '</p>').join('') }}<h3>Timeline</h3><p>{{ $json.timeline }}</p><p>Your manager will be discussing this learning path with you in your next 1-on-1 meeting.</p><p>We're invested in your growth and success!</p><p>Best regards,<br>The Learning & Development Team</p></body></html>",
        "options": {}
      },
      "name": "Email Learning Path to Employee",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        1780,
        420
      ],
      "id": "10"
    },
    {
      "parameters": {
        "channel": "#learning-development",
        "text": "New personalized learning path created for {{ $json.employeeName }}:\n\n*Position:* {{ $('Combine Employee Data').all().find(e => e.json.id === $json.employeeId).json.position }}\n*Department:* {{ $('Combine Employee Data').all().find(e => e.json.id === $json.employeeId).json.department }}\n\n*Skill Gaps Identified:* {{ $json.skillGaps.length }}\n*Learning Resources Recommended:* {{ $json.learningPath.length }}\n*Development Goals:* {{ $json.developmentGoals.length }}\n\nThe learning path has been emailed to the employee and saved to Google Docs."
      },
      "name": "Notify Managers",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [
        2000,
        420
      ],
      "id": "11"
Setup Instructions:

Connect your Human Resources system to n8n
Connect your PostgreSQL database to n8n
Connect your OpenAI account to n8n
Connect your Google Docs account to n8n
Connect your email service to n8n
Connect your Slack account to n8n
Create a "learning-paths" folder in Google Drive
Create an "employee_learning_paths" table in your PostgreSQL database with appropriate columns
Create an "employee_skills" table in your PostgreSQL database with appropriate columns
Create a "performance_reviews" table in your PostgreSQL database with appropriate columns
Update the Slack channel name to match your team's structure
These 15 workflows demonstrate the power of combining AI with n8n automation to solve complex business problems. Each workflow is designed to be fully functional and can be customized to fit specific business requirements. The workflows cover a wide range of business functions including customer support, marketing, sales, operations, finance, and human resources.

To implement these workflows:

Import the JSON code into your n8n instance
Connect the required services and databases
Configure any necessary credentials
Customize the workflow parameters to match your business needs
Test the workflow with sample data
Activate the workflow for production use
These workflows showcase how AI can be integrated into business processes to improve efficiency, decision-making, and customer experiences. By leveraging the power of n8n and AI, businesses can automate complex tasks that previously required significant manual effort.

