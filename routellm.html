<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>n8n AI Workflow Hub - Automation Gallery</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .logo i {
            font-size: 2rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: #555;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* Hero Section */
        .hero {
            text-align: center;
            padding: 4rem 0;
            color: white;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .search-container {
            max-width: 600px;
            margin: 0 auto 2rem;
            position: relative;
        }

        .search-box {
            width: 100%;
            padding: 1rem 1.5rem 1rem 3rem;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            outline: none;
        }

        .search-icon {
            position: absolute;
            left: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
        }

        /* Filters */
        .filters {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 1.5rem;
        }

        .filter-tab {
            padding: 0.7rem 1.5rem;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            background: white;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .filter-tab.active,
        .filter-tab:hover {
            border-color: #667eea;
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* Workflow Grid */
        .workflows-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            padding: 2rem 0;
        }

        .workflow-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .workflow-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .workflow-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .workflow-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .workflow-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .workflow-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.3rem;
        }

        .workflow-category {
            font-size: 0.85rem;
            color: #667eea;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .workflow-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .workflow-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .tag {
            padding: 0.3rem 0.8rem;
            background: #f0f4ff;
            color: #667eea;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .workflow-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #888;
        }

        .stat {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .workflow-actions {
            display: flex;
            gap: 0.8rem;
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* Complexity indicators */
        .complexity {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .complexity.beginner {
            background: #d4edda;
            color: #155724;
        }

        .complexity.intermediate {
            background: #fff3cd;
            color: #856404;
        }

        .complexity.advanced {
            background: #f8d7da;
            color: #721c24;
        }

        /* Footer */
        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            text-align: center;
            padding: 2rem 0;
            margin-top: 4rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .workflows-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                display: none;
            }
        }

        /* Loading animation */
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
            color: white;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <span>n8n AI Hub</span>
                </div>
                <ul class="nav-links">
                    <li><a href="#workflows">Workflows</a></li>
                    <li><a href="#categories">Categories</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h1>n8n AI Workflow Gallery</h1>
            <p>Discover powerful automation workflows powered by AI. From simple tasks to complex business processes, find the perfect n8n workflow for your needs.</p>
            
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-box" placeholder="Search workflows, categories, or technologies..." id="searchInput">
            </div>
        </div>
    </section>

    <section class="filters">
        <div class="container">
            <div class="filter-tabs">
                <div class="filter-tab active" data-category="all">All Workflows</div>
                <div class="filter-tab" data-category="ai-ml">AI & ML</div>
                <div class="filter-tab" data-category="data-processing">Data Processing</div>
                <div class="filter-tab" data-category="marketing">Marketing</div>
                <div class="filter-tab" data-category="productivity">Productivity</div>
                <div class="filter-tab" data-category="ecommerce">E-commerce</div>
                <div class="filter-tab" data-category="social-media">Social Media</div>
                <div class="filter-tab" data-category="finance">Finance</div>
                <div class="filter-tab" data-category="communication">Communication</div>
            </div>
        </div>
    </section>

    <main class="container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Loading workflows...</p>
        </div>
        
        <div class="workflows-grid" id="workflowsGrid">
            <!-- Workflows will be populated by JavaScript -->
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 n8n AI Workflow Hub. Empowering automation with artificial intelligence.</p>
        </div>
    </footer>

    <script>
        // Comprehensive workflow data with 50+ complex workflows
        const workflows = [
            {
                id: 1,
                title: "AI-Powered Customer Support Automation",
                category: "ai-ml",
                description: "Automatically categorize, prioritize, and respond to customer inquiries using OpenAI GPT-4, with sentiment analysis and escalation rules.",
                icon: "fas fa-headset",
                tags: ["OpenAI", "Sentiment Analysis", "Slack", "Email", "Zendesk"],
                complexity: "advanced",
                downloads: 1250,
                likes: 89,
                nodes: 15
            },
            {
                id: 2,
                title: "Smart Content Generation Pipeline",
                category: "ai-ml",
                description: "Generate blog posts, social media content, and marketing copy using AI, with automatic SEO optimization and multi-platform publishing.",
                icon: "fas fa-pen-fancy",
                tags: ["GPT-4", "SEO", "WordPress", "Twitter", "LinkedIn"],
                complexity: "intermediate",
                downloads: 980,
                likes: 76,
                nodes: 12
            },
            {
                id: 3,
                title: "Intelligent Lead Scoring & Nurturing",
                category: "marketing",
                description: "Score leads using ML algorithms, trigger personalized email sequences, and sync with CRM systems for optimal conversion rates.",
                icon: "fas fa-chart-line",
                tags: ["Machine Learning", "HubSpot", "Mailchimp", "Salesforce"],
                complexity: "advanced",
                downloads: 756,
                likes: 62,
                nodes: 18
            },
            {
                id: 4,
                title: "Real-time Data Sync & Transformation",
                category: "data-processing",
                description: "Sync data between multiple databases, APIs, and cloud services with real-time transformation and validation rules.",
                icon: "fas fa-database",
                tags: ["PostgreSQL", "MongoDB", "REST API", "AWS S3"],
                complexity: "intermediate",
                downloads: 1100,
                likes: 94,
                nodes: 10
            },
            {
                id: 5,
                title: "Social Media Sentiment Monitor",
                category: "social-media",
                description: "Monitor brand mentions across social platforms, analyze sentiment, and trigger alerts for negative feedback or opportunities.",
                icon: "fas fa-chart-pie",
                tags: ["Twitter API", "Sentiment Analysis", "Slack", "Discord"],
                complexity: "intermediate",
                downloads: 890,
                likes: 71,
                nodes: 14
            },
            {
                id: 6,
                title: "E-commerce Inventory Optimizer",
                category: "ecommerce",
                description: "Predict inventory needs using sales data, automatically reorder products, and optimize pricing based on market trends.",
                icon: "fas fa-boxes",
                tags: ["Shopify", "WooCommerce", "Predictive Analytics", "Pricing"],
                complexity: "advanced",
                downloads: 645,
                likes: 58,
                nodes: 16
            },
            {
                id: 7,
                title: "Automated Financial Reporting",
                category: "finance",
                description: "Generate comprehensive financial reports, track KPIs, and send automated insights to stakeholders with beautiful visualizations.",
                icon: "fas fa-chart-bar",
                tags: ["QuickBooks", "Excel", "Google Sheets", "Tableau"],
                complexity: "intermediate",
                downloads: 720,
                likes: 65,
                nodes: 11
            },
            {
                id: 8,
                title: "Smart Meeting Scheduler",
                category: "productivity",
                description: "AI-powered meeting scheduling that considers preferences, time zones, and availability across multiple calendar systems.",
                icon: "fas fa-calendar-alt",
                tags: ["Google Calendar", "Outlook", "Calendly", "AI Scheduling"],
                complexity: "intermediate",
                downloads: 1340,
                likes: 102,
                nodes: 9
            },
            {
                id: 9,
                title: "Multi-Channel Communication Hub",
                category: "communication",
                description: "Centralize communications from email, chat, SMS, and social media into a unified dashboard with smart routing.",
                icon: "fas fa-comments",
                tags: ["Slack", "Teams", "WhatsApp", "Email", "SMS"],
                complexity: "advanced",
                downloads: 567,
                likes: 43,
                nodes: 20
            },
            {
                id: 10,
                title: "Document AI Processing Pipeline",
                category: "ai-ml",
                description: "Extract, classify, and process documents using OCR and NLP, with automatic data entry and validation.",
                icon: "fas fa-file-alt",
                tags: ["OCR", "NLP", "Document AI", "Google Drive", "Dropbox"],
                complexity: "advanced",
                downloads: 834,
                likes: 67,
                nodes: 17
            },
            {
                id: 11,
                title: "Automated Code Review Assistant",
                category: "productivity",
                description: "AI-powered code review that checks for bugs, security issues, and best practices across multiple programming languages.",
                icon: "fas fa-code",
                tags: ["GitHub", "GitLab", "Code Analysis", "Security", "AI"],
                complexity: "advanced",
                downloads: 923,
                likes: 88,
                nodes: 13
            },
            {
                id: 12,
                title: "Smart Email Marketing Optimizer",
                category: "marketing",
                description: "Optimize email campaigns using A/B testing, send time optimization, and personalized content generation.",
                icon: "fas fa-envelope",
                tags: ["Mailchimp", "A/B Testing", "Personalization", "Analytics"],
                complexity: "intermediate",
                downloads: 1156,
                likes: 91,
                nodes: 12
            },
            {
                id: 13,
                title: "IoT Data Collection & Analysis",
                category: "data-processing",
                description: "Collect data from IoT devices, perform real-time analysis, and trigger actions based on sensor readings.",
                icon: "fas fa-microchip",
                tags: ["IoT", "MQTT", "InfluxDB", "Grafana", "Alerts"],
                complexity: "advanced",
                downloads: 445,
                likes: 34,
                nodes: 19
            },
            {
                id: 14,
                title: "Automated Expense Management",
                category: "finance",
                description: "Capture receipts, categorize expenses using AI, and automatically submit expense reports with approval workflows.",
                icon: "fas fa-receipt",
                tags: ["OCR", "Expense Tracking", "Approval Workflow", "Accounting"],
                complexity: "intermediate",
                downloads: 678,
                likes: 56,
                nodes: 14
            },
            {
                id: 15,
                title: "Social Media Content Scheduler",
                category: "social-media",
                description: "Schedule and publish content across multiple social platforms with optimal timing and hashtag suggestions.",
                icon: "fas fa-clock",
                tags: ["Facebook", "Instagram", "Twitter", "LinkedIn", "Scheduling"],
                complexity: "beginner",
                downloads: 1890,
                likes: 145,
                nodes: 8
            },
            {
                id: 16,
                title: "Customer Journey Analytics",
                category: "marketing",
                description: "Track customer interactions across touchpoints, identify conversion patterns, and optimize the customer journey.",
                icon: "fas fa-route",
                tags: ["Analytics", "Customer Journey", "Conversion", "Tracking"],
                complexity: "advanced",
                downloads: 567,
                likes: 42,
                nodes: 21
            },
            {
                id: 17,
                title: "Automated Backup & Sync System",
                category: "productivity",
                description: "Automatically backup files across cloud services, with versioning, encryption, and disaster recovery features.",
                icon: "fas fa-cloud-upload-alt",
                tags: ["Backup", "Cloud Storage", "Encryption", "Disaster Recovery"],
                complexity: "intermediate",
                downloads: 789,
                likes: 63,
                nodes: 10
            },
            {
                id: 18,
                title: "AI-Powered Recruitment Pipeline",
                category: "ai-ml",
                description: "Screen resumes, schedule interviews, and rank candidates using AI with bias detection and compliance features.",
                icon: "fas fa-user-tie",
                tags: ["Resume Screening", "AI Recruitment", "Interview Scheduling"],
                complexity: "advanced",
                downloads: 456,
                likes: 38,
                nodes: 16
            },
            {
                id: 19,
                title: "Dynamic Pricing Engine",
                category: "ecommerce",
                description: "Automatically adjust product prices based on demand, competition, and inventory levels using machine learning.",
                icon: "fas fa-tags",
                tags: ["Dynamic Pricing", "Machine Learning", "Competition Analysis"],
                complexity: "advanced",
                downloads: 334,
                likes: 29,
                nodes: 18
            },
            {
                id: 20,
                title: "Automated Quality Assurance",
                category: "productivity",
                description: "Run automated tests, generate reports, and notify teams of issues with comprehensive QA workflows.",
                icon: "fas fa-check-circle",
                tags: ["Testing", "QA", "Automation", "Reporting", "Notifications"],
                complexity: "intermediate",
                downloads: 612,
                likes: 48,
                nodes: 13
            },
            {
                id: 21,
                title: "Smart Inventory Forecasting",
                category: "ecommerce",
                description: "Predict future inventory needs using historical data, seasonality, and market trends with ML algorithms.",
                icon: "fas fa-chart-area",
                tags: ["Forecasting", "Machine Learning", "Inventory", "Analytics"],
                complexity: "advanced",
                downloads: 423,
                likes: 35,
                nodes: 15
            },
            {
                id: 22,
                title: "Multi-Language Content Translator",
                category: "ai-ml",
                description: "Automatically translate content across multiple languages with context awareness and quality validation.",
                icon: "fas fa-language",
                tags: ["Translation", "Multi-language", "Content", "AI"],
                complexity: "intermediate",
                downloads: 756,
                likes: 61,
                nodes: 11
            },
            {
                id: 23,
                title: "Automated Invoice Processing",
                category: "finance",
                description: "Extract data from invoices, validate information, and process payments with approval workflows.",
                icon: "fas fa-file-invoice",
                tags: ["Invoice Processing", "OCR", "Payment", "Workflow"],
                complexity: "intermediate",
                downloads: 890,
                likes: 72,
                nodes: 12
            },
            {
                id: 24,
                title: "Real-time Fraud Detection",
                category: "finance",
                description: "Monitor transactions in real-time, detect fraudulent patterns, and trigger immediate security responses.",
                icon: "fas fa-shield-alt",
                tags: ["Fraud Detection", "Security", "Real-time", "Machine Learning"],
                complexity: "advanced",
                downloads: 345,
                likes: 28,
                nodes: 19
            },
            {
                id: 25,
                title: "Automated SEO Optimizer",
                category: "marketing",
                description: "Analyze website performance, suggest SEO improvements, and automatically implement optimization strategies.",
                icon: "fas fa-search",
                tags: ["SEO", "Website Optimization", "Analytics", "Automation"],
                complexity: "intermediate",
                downloads: 1023,
                likes: 84,
                nodes: 14
            },
            {
                id: 26,
                title: "Smart Task Prioritization",
                category: "productivity",
                description: "Automatically prioritize tasks based on deadlines, importance, and resource availability using AI algorithms.",
                icon: "fas fa-sort-amount-up",
                tags: ["Task Management", "Prioritization", "AI", "Productivity"],
                complexity: "intermediate",
                downloads: 1245,
                likes: 98,
                nodes: 9
            },
            {
                id: 27,
                title: "Automated Compliance Monitoring",
                category: "finance",
                description: "Monitor business activities for compliance violations, generate reports, and alert stakeholders of issues.",
                icon: "fas fa-gavel",
                tags: ["Compliance", "Monitoring", "Reporting", "Alerts"],
                complexity: "advanced",
                downloads: 234,
                likes: 19,
                nodes: 17
            },
            {
                id: 28,
                title: "Customer Feedback Analyzer",
                category: "ai-ml",
                description: "Collect and analyze customer feedback from multiple sources, extract insights, and generate action items.",
                icon: "fas fa-comment-dots",
                tags: ["Feedback Analysis", "Sentiment", "Insights", "Customer"],
                complexity: "intermediate",
                downloads: 678,
                likes: 54,
                nodes: 13
            },
            {
                id: 29,
                title: "Automated Report Generator",
                category: "data-processing",
                description: "Generate comprehensive reports from multiple data sources with customizable templates and scheduling.",
                icon: "fas fa-file-pdf",
                tags: ["Reporting", "Data Visualization", "Templates", "Automation"],
                complexity: "intermediate",
                downloads: 945,
                likes: 76,
                nodes: 11
            },
            {
                id: 30,
                title: "Smart Chatbot Builder",
                category: "ai-ml",
                description: "Create intelligent chatbots with natural language processing, context awareness, and multi-platform deployment.",
                icon: "fas fa-robot",
                tags: ["Chatbot", "NLP", "Conversational AI", "Multi-platform"],
                complexity: "advanced",
                downloads: 567,
                likes: 45,
                nodes: 16
            },
            {
                id: 31,
                title: "Automated Video Processing",
                category: "ai-ml",
                description: "Process videos with AI for transcription, object detection, and content moderation with cloud integration.",
                icon: "fas fa-video",
                tags: ["Video Processing", "AI", "Transcription", "Content Moderation"],
                complexity: "advanced",
                downloads: 389,
                likes: 31,
                nodes: 18
            },
            {
                id: 32,
                title: "Smart Email Classifier",
                category: "productivity",
                description: "Automatically classify and route emails based on content, sender, and priority using machine learning.",
                icon: "fas fa-inbox",
                tags: ["Email Classification", "Machine Learning", "Automation"],
                complexity: "intermediate",
                downloads: 823,
                likes: 67,
                nodes: 10
            },
            {
                id: 33,
                title: "Predictive Maintenance System",
                category: "data-processing",
                description: "Monitor equipment health, predict failures, and schedule maintenance using IoT data and ML algorithms.",
                icon: "fas fa-tools",
                tags: ["Predictive Maintenance", "IoT", "Machine Learning", "Monitoring"],
                complexity: "advanced",
                downloads: 445,
                likes: 36,
                nodes: 20
            },
            {
                id: 34,
                title: "Automated A/B Test Manager",
                category: "marketing",
                description: "Set up, run, and analyze A/B tests automatically with statistical significance testing and reporting.",
                icon: "fas fa-flask",
                tags: ["A/B Testing", "Statistics", "Experimentation", "Analytics"],
                complexity: "intermediate",
                downloads: 656,
                likes: 52,
                nodes: 12
            },
            {
                id: 35,
                title: "Smart Document Workflow",
                category: "productivity",
                description: "Automate document creation, review, approval, and distribution with version control and notifications.",
                icon: "fas fa-file-contract",
                tags: ["Document Workflow", "Approval", "Version Control"],
                complexity: "intermediate",
                downloads: 734,
                likes: 59,
                nodes: 14
            },
            {
                id: 36,
                title: "Real-time Stock Analyzer",
                category: "finance",
                description: "Monitor stock prices, analyze trends, and execute trades based on predefined strategies and market conditions.",
                icon: "fas fa-chart-candlestick",
                tags: ["Stock Analysis", "Trading", "Real-time", "Financial Data"],
                complexity: "advanced",
                downloads: 512,
                likes: 41,
                nodes: 17
            },
            {
                id: 37,
                title: "Automated Content Curation",
                category: "marketing",
                description: "Curate relevant content from multiple sources, schedule posts, and engage with audiences across platforms.",
                icon: "fas fa-rss",
                tags: ["Content Curation", "Social Media", "Automation", "Engagement"],
                complexity: "intermediate",
                downloads: 889,
                likes: 71,
                nodes: 13
            },
            {
                id: 38,
                title: "Smart Appointment Booking",
                category: "productivity",
                description: "Intelligent appointment booking system with availability checking, reminders, and calendar integration.",
                icon: "fas fa-calendar-check",
                tags: ["Appointment Booking", "Calendar", "Reminders", "Integration"],
                complexity: "beginner",
                downloads: 1456,
                likes: 118,
                nodes: 8
            },
            {
                id: 39,
                title: "Automated Competitor Analysis",
                category: "marketing",
                description: "Monitor competitor activities, pricing, and marketing strategies with automated reporting and alerts.",
                icon: "fas fa-binoculars",
                tags: ["Competitor Analysis", "Monitoring", "Pricing", "Marketing"],
                complexity: "intermediate",
                downloads: 623,
                likes: 50,
                nodes: 15
            },
            {
                id: 40,
                title: "Smart Data Validation Pipeline",
                category: "data-processing",
                description: "Validate data quality, detect anomalies, and clean datasets automatically with comprehensive reporting.",
                icon: "fas fa-check-double",
                tags: ["Data Validation", "Quality Control", "Anomaly Detection"],
                complexity: "intermediate",
                downloads: 567,
                likes: 45,
                nodes: 11
            },
            {
                id: 41,
                title: "Automated Survey Processor",
                category: "ai-ml",
                description: "Process survey responses, extract insights, and generate reports with sentiment analysis and visualization.",
                icon: "fas fa-poll",
                tags: ["Survey Processing", "Analytics", "Sentiment Analysis"],
                complexity: "intermediate",
                downloads: 445,
                likes: 36,
                nodes: 12
            },
            {
                id: 42,
                title: "Smart Notification System",
                category: "communication",
                description: "Intelligent notification routing based on urgency, recipient preferences, and communication channels.",
                icon: "fas fa-bell",
                tags: ["Notifications", "Smart Routing", "Multi-channel"],
                complexity: "intermediate",
                downloads: 789,
                likes: 63,
                nodes: 10
            },
            {
                id: 43,
                title: "Automated Image Recognition",
                category: "ai-ml",
                description: "Classify and tag images automatically using computer vision, with custom model training capabilities.",
                icon: "fas fa-eye",
                tags: ["Image Recognition", "Computer Vision", "Classification"],
                complexity: "advanced",
                downloads: 356,
                likes: 29,
                nodes: 16
            },
            {
                id: 44,
                title: "Smart Workflow Optimizer",
                category: "productivity",
                description: "Analyze workflow performance, identify bottlenecks, and suggest optimizations using process mining.",
                icon: "fas fa-project-diagram",
                tags: ["Workflow Optimization", "Process Mining", "Analytics"],
                complexity: "advanced",
                downloads: 234,
                likes: 19,
                nodes: 18
            },
            {
                id: 45,
                title: "Automated Risk Assessment",
                category: "finance",
                description: "Assess financial and operational risks using multiple data sources with predictive modeling and alerts.",
                icon: "fas fa-exclamation-triangle",
                tags: ["Risk Assessment", "Predictive Modeling", "Financial Analysis"],
                complexity: "advanced",
                downloads: 345,
                likes: 28,
                nodes: 19
            },
            {
                id: 46,
                title: "Smart Content Personalization",
                category: "marketing",
                description: "Personalize website content and recommendations based on user behavior and preferences using AI.",
                icon: "fas fa-user-cog",
                tags: ["Personalization", "User Behavior", "Recommendations", "AI"],
                complexity: "advanced",
                downloads: 567,
                likes: 45,
                nodes: 17
            },
            {
                id: 47,
                title: "Automated Database Optimizer",
                category: "data-processing",
                description: "Monitor database performance, optimize queries, and maintain data integrity with automated maintenance.",
                icon: "fas fa-database",
                tags: ["Database Optimization", "Performance", "Maintenance"],
                complexity: "advanced",
                downloads: 423,
                likes: 34,
                nodes: 15
            },
            {
                id: 48,
                title: "Smart Event Processor",
                category: "data-processing",
                description: "Process real-time events, apply business rules, and trigger actions with complex event processing.",
                icon: "fas fa-bolt",
                tags: ["Event Processing", "Real-time", "Business Rules"],
                complexity: "advanced",
                downloads: 334,
                likes: 27,
                nodes: 20
            },
            {
                id: 49,
                title: "Automated Knowledge Base",
                category: "ai-ml",
                description: "Build and maintain a knowledge base automatically from documents, with AI-powered search and Q&A.",
                icon: "fas fa-brain",
                tags: ["Knowledge Base", "AI Search", "Document Processing"],
                complexity: "advanced",
                downloads: 456,
                likes: 37,
                nodes: 16
            },
            {
                id: 50,
                title: "Smart Resource Allocator",
                category: "productivity",
                description: "Optimize resource allocation across projects and teams using constraint programming and AI algorithms.",
                icon: "fas fa-balance-scale",
                tags: ["Resource Allocation", "Optimization", "Project Management"],
                complexity: "advanced",
                downloads: 289,
                likes: 23,
                nodes: 18
            },
            {
                id: 51,
                title: "Automated Trend Detector",
                category: "ai-ml",
                description: "Detect emerging trends in data streams, social media, and market data with predictive analytics.",
                icon: "fas fa-trending-up",
                tags: ["Trend Detection", "Predictive Analytics", "Social Media"],
                complexity: "advanced",
                downloads: 378,
                likes: 30,
                nodes: 17
            },
            {
                id: 52,
                title: "Smart Contract Analyzer",
                category: "finance",
                description: "Analyze smart contracts for security vulnerabilities, gas optimization, and compliance issues.",
                icon: "fas fa-file-contract",
                tags: ["Smart Contracts", "Security", "Blockchain", "Analysis"],
                complexity: "advanced",
                downloads: 234,
                likes: 19,
                nodes: 19
            }
        ];

        // DOM elements
        const searchInput = document.getElementById('searchInput');
        const workflowsGrid = document.getElementById('workflowsGrid');
        const filterTabs = document.querySelectorAll('.filter-tab');
        const loading = document.getElementById('loading');

        let currentFilter = 'all';
        let searchTerm = '';

        // Initialize the app
        function init() {
            renderWorkflows(workflows);
            setupEventListeners();
        }

        // Setup event listeners
        function setupEventListeners() {
            searchInput.addEventListener('input', handleSearch);
            filterTabs.forEach(tab => {
                tab.addEventListener('click', handleFilterChange);
            });
        }

        // Handle search
        function handleSearch(e) {
            searchTerm = e.target.value.toLowerCase();
            filterAndRenderWorkflows();
        }

        // Handle filter change
        function handleFilterChange(e) {
            filterTabs.forEach(tab => tab.classList.remove('active'));
            e.target.classList.add('active');
            currentFilter = e.target.dataset.category;
            filterAndRenderWorkflows();
        }

        // Filter and render workflows
        function filterAndRenderWorkflows() {
            showLoading();
            
            setTimeout(() => {
                let filteredWorkflows = workflows;

                // Apply category filter
                if (currentFilter !== 'all') {
                    filteredWorkflows = filteredWorkflows.filter(workflow => 
                        workflow.category === currentFilter
                    );
                }

                // Apply search filter
                if (searchTerm) {
                    filteredWorkflows = filteredWorkflows.filter(workflow =>
                        workflow.title.toLowerCase().includes(searchTerm) ||
                        workflow.description.toLowerCase().includes(searchTerm) ||
                        workflow.tags.some(tag => tag.toLowerCase().includes(searchTerm))
                    );
                }

                hideLoading();
                renderWorkflows(filteredWorkflows);
            }, 300);
        }

        // Show loading
        function showLoading() {
            loading.style.display = 'block';
            workflowsGrid.style.opacity = '0.5';
        }

        // Hide loading
        function hideLoading() {
            loading.style.display = 'none';
            workflowsGrid.style.opacity = '1';
        }

        // Render workflows
        function renderWorkflows(workflowsToRender) {
            if (workflowsToRender.length === 0) {
                workflowsGrid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: white;">
                        <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h3>No workflows found</h3>
                        <p>Try adjusting your search or filter criteria.</p>
                    </div>
                `;
                return;
            }

            workflowsGrid.innerHTML = workflowsToRender.map(workflow => `
                <div class="workflow-card" data-category="${workflow.category}">
                    <div class="complexity ${workflow.complexity}">${workflow.complexity}</div>
                    <div class="workflow-header">
                        <div class="workflow-icon">
                            <i class="${workflow.icon}"></i>
                        </div>
                        <div>
                            <div class="workflow-title">${workflow.title}</div>
                            <div class="workflow-category">${workflow.category.replace('-', ' ')}</div>
                        </div>
                    </div>
                    <div class="workflow-description">${workflow.description}</div>
                    <div class="workflow-tags">
                        ${workflow.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div class="workflow-stats">
                        <div class="stat">
                            <i class="fas fa-download"></i>
                            <span>${workflow.downloads}</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>${workflow.likes}</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-sitemap"></i>
                            <span>${workflow.nodes} nodes</span>
                        </div>
                    </div>
                    <div class="workflow-actions">
                        <a href="#" class="btn btn-primary" onclick="downloadWorkflow(${workflow.id})">
                            <i class="fas fa-download"></i>
                            Download
                        </a>
                        <a href="#" class="btn btn-secondary" onclick="previewWorkflow(${workflow.id})">
                            <i class="fas fa-eye"></i>
                            Preview
                        </a>
                    </div>
                </div>
            `).join('');
        }

        // Download workflow
        function downloadWorkflow(id) {
            const workflow = workflows.find(w => w.id === id);
            alert(`Downloading workflow: ${workflow.title}\n\nThis would typically download the n8n workflow JSON file.`);
        }

        // Preview workflow
        function previewWorkflow(id) {
            const workflow = workflows.find(w => w.id === id);
            alert(`Preview workflow: ${workflow.title}\n\nThis would typically open a detailed view or diagram of the workflow.`);
        }

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>