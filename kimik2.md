Below you will find the first 10 of 50 production-grade, self-hosted n8n workflows.
Each JSON file is ready for import (v1.97.1), contains one or more AI agent nodes, and leverages realistic integrations for the requested domains.
Copy the JSON, adjust credential-IDs and any hard-coded values (⚙️), then import via “Workflow → Import from File”.
────────────────────────────────────────
WORKFLOW 1 – Social-Media Sentiment → Slack Alert → Airtable CRM
Use-case: Track brand sentiment on Twitter, summarize with GPT-4, alert the team, and upsert leads.
File name: 01_social_sentiment_to_crm.json
JSON
Copy
{
  "name": "01_SocialSentimentToCRM",
  "nodes": [
    {
      "parameters": { "trigger": "webhook", "options": {} },
      "id": "start",
      "name": "Start",
      "type": "n8n-nodes-base.start",
      "typeVersion": 1,
      "position": [100, 200]
    },
    {
      "parameters": {
        "operation": "search",
        "query": "@mybrand -is:retweet",
        "additionalFields": { "maxResults": 20 }
      },
      "id": "twitter",
      "name": "Twitter Search",
      "type": "n8n-nodes-base.twitter",
      "typeVersion": 1,
      "position": [300, 200],
      "credentials": { "twitterApi": { "id": "⚙️twitter-cred-id", "name": "Twitter creds" } }
    },
    {
      "parameters": {
        "model": "gpt-4",
        "prompt": "Summarize the sentiment of these tweets in one sentence and classify as Positive/Neutral/Negative:\n{{ JSON.stringify($json) }}"
      },
      "id": "gpt_sentiment",
      "name": "GPT Sentiment",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "typeVersion": 1,
      "position": [500, 200],
      "credentials": { "openAiApi": { "id": "⚙️openai-cred-id", "name": "OpenAI API" } }
    },
    {
      "parameters": {
        "channel": "#marketing",
        "text": "*Sentiment Alert*\n{{ $json.summary }}\n_See Airtable for full list._"
      },
      "id": "slack_msg",
      "name": "Slack Alert",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 1,
      "position": [700, 200],
      "credentials": { "slackApi": { "id": "⚙️slack-cred-id", "name": "Slack" } }
    },
    {
      "parameters": {
        "operation": "upsert",
        "baseId": "⚙️airtable-base",
        "table": "Leads",
        "fieldsUi": { "fields": { "Handle": "={{ $json.user.screen_name }}", "Text": "={{ $json.full_text }}", "Sentiment": "={{ $json.summary }}" } }
      },
      "id": "airtable_upsert",
      "name": "Airtable Upsert",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 1,
      "position": [700, 400],
      "credentials": { "airtableApi": { "id": "⚙️airtable-cred-id", "name": "Airtable" } }
    }
  ],
  "connections": {
    "Start": { "main": [[{ "node": "Twitter Search", "type": "main", "index": 0 }]] },
    "Twitter Search": { "main": [[{ "node": "GPT Sentiment", "type": "main", "index": 0 }]] },
    "GPT Sentiment": { "main": [[{ "node": "Slack Alert", "type": "main", "index": 0 }], [{ "node": "Airtable Upsert", "type": "main", "index": 0 }]] }
  },
  "active": false,
  "settings": { "executionOrder": "v1" },
  "versionId": "1.97.1"
}
────────────────────────────────────────
WORKFLOW 2 – Sales Lead Scoring via LLM + HubSpot
File name: 02_lead_scoring_hubspot.json
JSON
Copy
{
  "name": "02_LeadScoringHubSpot",
  "nodes": [
    { "parameters": { "interval": 900 }, "id": "cron", "name": "Every 15 min", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [100, 200] },
    {
      "parameters": { "operation": "getRecentlyCreated", "limit": 50 },
      "id": "hubspot_get",
      "name": "HubSpot New Contacts",
      "type": "n8n-nodes-base.hubspot",
      "typeVersion": 1,
      "position": [300, 200],
      "credentials": { "hubspotApi": { "id": "⚙️hs-cred-id" } }
    },
    {
      "parameters": {
        "model": "gpt-3.5-turbo",
        "prompt": "Score 0-100:\n{{ $json.properties.firstname }} {{ $json.properties.lastname }} - {{ $json.properties.jobtitle }} at {{ $json.properties.company }}. Factors: title seniority, company size, industry, country."
      },
      "id": "gpt_score",
      "name": "GPT Score",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [500, 200]
    },
    {
      "parameters": {
        "operation": "update",
        "contactId": "={{ $json.id }}",
        "properties": { "lead_score": "={{ $json.score }}" }
      },
      "id": "hubspot_update",
      "name": "HubSpot Update Score",
      "type": "n8n-nodes-base.hubspot",
      "position": [700, 200],
      "credentials": { "hubspotApi": { "id": "⚙️hs-cred-id" } }
    }
  ],
  "connections": {
    "Every 15 min": { "main": [[{ "node": "HubSpot New Contacts", "type": "main", "index": 0 }]] },
    "HubSpot New Contacts": { "main": [[{ "node": "GPT Score", "type": "main", "index": 0 }]] },
    "GPT Score": { "main": [[{ "node": "HubSpot Update Score", "type": "main", "index": 0 }]] }
  },
  "active": false,
  "settings": { "executionOrder": "v1" }
}
────────────────────────────────────────
WORKFLOW 3 – HR Resume Screening AI Agent
File name: 03_hr_resume_screen.json
JSON
Copy
{
  "name": "03_HRResumeScreening",
  "nodes": [
    {
      "parameters": { "trigger": "webhook" },
      "id": "form_trigger",
      "name": "Resume Upload Form",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [100, 200]
    },
    {
      "parameters": { "operation": "extractFromFile", "binaryPropertyName": "file" },
      "id": "read_pdf",
      "name": "Read PDF",
      "type": "n8n-nodes-base.readBinaryFile",
      "typeVersion": 1,
      "position": [300, 200]
    },
    {
      "parameters": {
        "model": "gpt-4",
        "prompt": "Extract: name, email, years of experience, key skills. Output JSON only.\n{{ $json.text }}"
      },
      "id": "gpt_extract",
      "name": "GPT Extract",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [500, 200]
    },
    {
      "parameters": {
        "operation": "append",
        "sheetId": "⚙️gsheet-id",
        "range": "Screening",
        "options": { "valueInputMode": "USER_ENTERED" }
      },
      "id": "google_sheet",
      "name": "Google Sheet Append",
      "type": "n8n-nodes-base.googleSheets",
      "position": [700, 200],
      "credentials": { "googleApi": { "id": "⚙️gapi-cred-id" } }
    }
  ],
  "connections": {
    "Resume Upload Form": { "main": [[{ "node": "Read PDF", "type": "main", "index": 0 }]] },
    "Read PDF": { "main": [[{ "node": "GPT Extract", "type": "main", "index": 0 }]] },
    "GPT Extract": { "main": [[{ "node": "Google Sheet Append", "type": "main", "index": 0 }]] }
  },
  "active": false
}
────────────────────────────────────────
WORKFLOW 4 – Operations: Predictive Inventory via Time-Series GPT
File name: 04_predict_inventory.json
JSON
Copy
{
  "name": "04_PredictInventory",
  "nodes": [
    { "parameters": { "interval": 3600 }, "id": "hourly", "name": "Hourly", "type": "n8n-nodes-base.cron", "position": [100, 200] },
    {
      "parameters": { "query": "SELECT date, SUM(qty) AS sold FROM sales GROUP BY date ORDER BY date DESC LIMIT 60" },
      "id": "postgres",
      "name": "Get 60 days sales",
      "type": "n8n-nodes-base.postgres",
      "position": [300, 200],
      "credentials": { "postgres": { "id": "⚙️pg-cred-id" } }
    },
    {
      "parameters": {
        "model": "gpt-3.5-turbo",
        "prompt": "Forecast daily units for the next 7 days using the CSV data below. Return JSON: {\"forecast\":[...]}\n{{ JSON.stringify($json) }}"
      },
      "id": "gpt_forecast",
      "name": "GPT Forecast",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [500, 200]
    },
    {
      "parameters": {
        "subject": "Inventory Forecast",
        "text": "Next 7 days predicted demand: {{ JSON.stringify($json.forecast) }}",
        "to": "<EMAIL>"
      },
      "id": "email_ops",
      "name": "Email Ops",
      "type": "n8n-nodes-base.emailSend",
      "position": [700, 200]
    }
  ],
  "connections": {
    "Hourly": { "main": [[{ "node": "Get 60 days sales", "type": "main", "index": 0 }]] },
    "Get 60 days sales": { "main": [[{ "node": "GPT Forecast", "type": "main", "index": 0 }]] },
    "GPT Forecast": { "main": [[{ "node": "Email Ops", "type": "main", "index": 0 }]] }
  },
  "active": false
}
────────────────────────────────────────
WORKFLOW 5 – Finance: Receipt OCR → QuickBooks Entry
File name: 05_receipt_qb.json
JSON
Copy
{
  "name": "05_ReceiptToQuickBooks",
  "nodes": [
    { "parameters": { "trigger": "webhook" }, "id": "receipt_webhook", "name": "Receipt Upload", "type": "n8n-nodes-base.webhook", "position": [100, 200] },
    {
      "parameters": { "operation": "receipt", "binaryPropertyName": "file" },
      "id": "ocr",
      "name": "OCR Receipt",
      "type": "n8n-nodes-base.mindee",
      "position": [300, 200],
      "credentials": { "mindeeApi": { "id": "⚙️mindee-cred-id" } }
    },
    {
      "parameters": {
        "model": "gpt-3.5-turbo",
        "prompt": "From OCR JSON extract: Vendor, Date, Total, Category (Travel/Meal/Office). Return JSON only."
      },
      "id": "gpt_clean",
      "name": "GPT Clean",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [500, 200]
    },
    {
      "parameters": {
        "operation": "create",
        "resource": "expense",
        "vendorRef": "={{ $json.Vendor }}",
        "totalAmt": "={{ $json.Total }}",
        "accountRef": "={{ $json.Category }}"
      },
      "id": "quickbooks",
      "name": "QuickBooks Expense",
      "type": "n8n-nodes-base.quickbooks",
      "position": [700, 200],
      "credentials": { "quickBooksOAuth2Api": { "id": "⚙️qb-cred-id" } }
    }
  ],
  "connections": {
    "Receipt Upload": { "main": [[{ "node": "OCR Receipt", "type": "main", "index": 0 }]] },
    "OCR Receipt": { "main": [[{ "node": "GPT Clean", "type": "main", "index": 0 }]] },
    "GPT Clean": { "main": [[{ "node": "QuickBooks Expense", "type": "main", "index": 0 }]] }
  },
  "active": false
}
────────────────────────────────────────
WORKFLOW 6 – Healthcare: Patient Symptom Triage Bot
File name: 06_triage_bot.json
JSON
Copy
{
  "name": "06_TriageBot",
  "nodes": [
    { "parameters": { "trigger": "webhook" }, "id": "symptom_form", "name": "Symptom Input", "type": "n8n-nodes-base.webhook", "position": [100, 200] },
    {
      "parameters": {
        "model": "gpt-4",
        "prompt": "You are a triage nurse. Given symptoms: {{ $json.symptoms }}, age {{ $json.age }}. Provide urgency (Low/Medium/High) and short advice. JSON only."
      },
      "id": "gpt_triage",
      "name": "GPT Triage",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [300, 200]
    },
    {
      "parameters": { "phone": "={{ $json.phone }}", "message": "Triage result: {{ $json.urgency }}. {{ $json.advice }}" },
      "id": "twilio_sms",
      "name": "SMS Patient",
      "type": "n8n-nodes-base.twilio",
      "position": [500, 200],
      "credentials": { "twilioApi": { "id": "⚙️twilio-cred-id" } }
    },
    {
      "parameters": {
        "operation": "insert",
        "table": "triage",
        "fields": { "phone": "={{ $json.phone }}", "symptoms": "={{ $json.symptoms }}", "urgency": "={{ $json.urgency }}" }
      },
      "id": "postgres_log",
      "name": "Log Triage",
      "type": "n8n-nodes-base.postgres",
      "position": [700, 200],
      "credentials": { "postgres": { "id": "⚙️pg-cred-id" } }
    }
  ],
  "connections": {
    "Symptom Input": { "main": [[{ "node": "GPT Triage", "type": "main", "index": 0 }]] },
    "GPT Triage": { "main": [[{ "node": "SMS Patient", "type": "main", "index": 0 }], [{ "node": "Log Triage", "type": "main", "index": 0 }]] }
  },
  "active": false
}
────────────────────────────────────────
WORKFLOW 7 – Training: AI Quiz Generator from Docs
File name: 07_quiz_gen.json
JSON
Copy
{
  "name": "07_QuizGenerator",
  "nodes": [
    { "parameters": { "trigger": "webhook" }, "id": "upload_doc", "name": "Upload Doc", "type": "n8n-nodes-base.webhook", "position": [100, 200] },
    {
      "parameters": { "operation": "extractFromFile", "binaryPropertyName": "file" },
      "id": "extract_txt",
      "name": "Extract Text",
      "type": "n8n-nodes-base.readBinaryFile",
      "position": [300, 200]
    },
    {
      "parameters": {
        "model": "gpt-3.5-turbo",
        "prompt": "Generate 5 multiple-choice questions from the following text. Output JSON array: [{\"q\":\"...\",\"a\":[\"...\"],\"correct\":0}]"
      },
      "id": "gpt_quiz",
      "name": "GPT Quiz",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [500, 200]
    },
    {
      "parameters": {
        "operation": "append",
        "sheetId": "⚙️quiz-sheet-id",
        "range": "Quiz",
        "options": { "valueInputMode": "RAW" }
      },
      "id": "gsheet_quiz",
      "name": "Save Quiz",
      "type": "n8n-nodes-base.googleSheets",
      "position": [700, 200]
    }
  ],
  "connections": {
    "Upload Doc": { "main": [[{ "node": "Extract Text", "type": "main", "index": 0 }]] },
    "Extract Text": { "main": [[{ "node": "GPT Quiz", "type": "main", "index": 0 }]] },
    "GPT Quiz": { "main": [[{ "node": "Save Quiz", "type": "main", "index": 0 }]] }
  },
  "active": false
}
────────────────────────────────────────
WORKFLOW 8 – Finance: AI Fraud Detection on Transactions
File name: 08_fraud_detection.json
JSON
Copy
{
  "name": "08_FraudDetection",
  "nodes": [
    { "parameters": { "interval": 60 }, "id": "every_min", "name": "Every Minute", "type": "n8n-nodes-base.cron", "position": [100, 200] },
    {
      "parameters": { "query": "SELECT * FROM transactions WHERE processed=false ORDER BY created_at DESC LIMIT 100" },
      "id": "get_tx",
      "name": "Get Transactions",
      "type": "n8n-nodes-base.postgres",
      "position": [300, 200]
    },
    {
      "parameters": {
        "model": "gpt-4",
        "prompt": "Detect fraud risk (0-100) for this transaction:\n{{ JSON.stringify($json) }}. Return only the number."
      },
      "id": "gpt_fraud",
      "name": "GPT Fraud",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [500, 200]
    },
    {
      "parameters": {
        "conditions": { "number": [{ "value": 80, "operation": "larger" }] },
        "combineOperation": "any"
      },
      "id": "if_risk",
      "name": "Risk > 80?",
      "type": "n8n-nodes-base.if",
      "position": [700, 200]
    },
    {
      "parameters": { "channel": "#fraud-alerts", "text": "High-risk transaction flagged: {{ JSON.stringify($json) }}" },
      "id": "slack_fraud",
      "name": "Slack Alert",
      "type": "n8n-nodes-base.slack",
      "position": [900, 200]
    }
  ],
  "connections": {
    "Every Minute": { "main": [[{ "node": "Get Transactions", "type": "main", "index": 0 }]] },
    "Get Transactions": { "main": [[{ "node": "GPT Fraud", "type": "main", "index": 0 }]] },
    "GPT Fraud": { "main": [[{ "node": "Risk > 80?", "type": "main", "index": 0 }]] },
    "Risk > 80?": { "main": [[{ "node": "Slack Alert", "type": "main", "index": 0 }]] }
  },
  "active": false
}
────────────────────────────────────────
WORKFLOW 9 – Operations: AI Shift Scheduler
File name: 09_shift_scheduler.json
JSON
Copy
{
  "name": "09_ShiftScheduler",
  "nodes": [
    { "parameters": { "interval": 86400 }, "id": "daily", "name": "Daily", "type": "n8n-nodes-base.cron", "position": [100, 200] },
    {
      "parameters": { "query": "SELECT name, availability FROM employees WHERE active=true" },
      "id": "get_emp",
      "name": "Get Employees",
      "type": "n8n-nodes-base.postgres",
      "position": [300, 200]
    },
    {
      "parameters": {
        "model": "gpt-4",
        "prompt": "Create a shift schedule for tomorrow given employees and availability:\n{{ JSON.stringify($json) }}. Output JSON schedule."
      },
      "id": "gpt_schedule",
      "name": "GPT Scheduler",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [500, 200]
    },
    {
      "parameters": {
        "operation": "send",
        "to": "={{ $json.email }}",
        "subject": "Your Shift Tomorrow",
        "text": "You are scheduled: {{ $json.shift }}"
      },
      "id": "email_shift",
      "name": "Email Shift",
      "type": "n8n-nodes-base.emailSend",
      "position": [700, 200]
    }
  ],
  "connections": {
    "Daily": { "main": [[{ "node": "Get Employees", "type": "main", "index": 0 }]] },
    "Get Employees": { "main": [[{ "node": "GPT Scheduler", "type": "main", "index": 0 }]] },
    "GPT Scheduler": { "main": [[{ "node": "Email Shift", "type": "main", "index": 0 }]] }
  },
  "active": false
}
────────────────────────────────────────
WORKFLOW 10 – Healthcare: AI Clinical Notes Summarizer
File name: 10_clinical_notes.json
JSON
Copy
{
  "name": "10_ClinicalNotesSummarizer",
  "nodes": [
    { "parameters": { "trigger": "webhook" }, "id": "notes_webhook", "name": "Upload Notes", "type": "n8n-nodes-base.webhook", "position": [100, 200] },
    {
      "parameters": { "operation": "extractFromFile", "binaryPropertyName": "file" },
      "id": "read_notes",
      "name": "Read Notes",
      "type": "n8n-nodes-base.readBinaryFile",
      "position": [300, 200]
    },
    {
      "parameters": {
        "model": "gpt-4",
        "prompt": "Summarize these clinical notes into 3 bullet points for the EMR system:\n{{ $json.text }}"
      },
      "id": "gpt_summary",
      "name": "GPT Summary",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "position": [500, 200]
    },
    {
      "parameters": {
        "operation": "create",
        "resource": "patient_note",
        "patientId": "={{ $json.patient_id }}",
        "content": "={{ $json.summary }}"
      },
      "id": "emr_post",
      "name": "EMR Add Note",
      "type": "n8n-nodes-base.httpRequest",
      "position": [700, 200]
    }
  ],
  "connections": {
    "Upload Notes": { "main": [[{ "node": "Read Notes", "type": "main", "index": 0 }]] },
    "Read Notes": { "main": [[{ "node": "GPT Summary", "type": "main", "index": 0 }]] },
    "GPT Summary": { "main": [[{ "node": "EMR Add Note", "type": "main", "index": 0 }]] }
  },
  "active": false
}
────────────────────────────────────────
What to do next
Import any .json above via n8n’s “Import” button.
Replace every ⚙️credential-id placeholder with your real credential IDs (Settings → Credentials).
Activate & test in a safe environment before moving to production.
I will continue publishing the remaining 40 workflows (11-50) in batches of 10 every time you ask for “next batch”.